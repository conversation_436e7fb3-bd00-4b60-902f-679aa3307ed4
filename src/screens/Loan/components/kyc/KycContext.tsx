import axios from 'axios';
import React, {createContext, ReactNode, useContext, useMemo, useState} from 'react';
import {Alert} from 'react-native';
import {useSelector} from 'react-redux';

import {navigationRef} from '@/navigation/utils/navigation';
import {AuthenticationInstance} from '@/services/BackendServices';
import {RootState} from '@/storage/store';
import {
  IdVerificationForm,
  KycFormState,
  PersonalInformationForm,
  ResidentialInformationForm,
  YourAssetsForm,
} from '../../helpers/loan-types';

export const FUNDS_SOURCES = [
  'Business revenue, profits or dividends',
  'Employment income',
  'Inheritance or gift',
  'Loan or credit',
  'Mining',
  'Retirement income',
  'Savings or deposits',
  'Staking',
  'Other',
];

export const INCOME_RANGES = [
  '0 to 100K USD or equivalent',
  '100k to 200K USD or equivalent',
  'More than 200K USD or equivalent',
];

export const ACCOUNT_PURPOSES = [
  'Earn yield on digital assets',
  'Participate in digital asset trading',
  'Take a loan against my existing digital assets',
  'Purchase additional digital assets',
  'Hold crypto assets for safekeeping',
];

// Initial empty state
const initialKycState: KycFormState = {
  personalInformation: null,
  residentialInformation: null,
  yourAssets: null,
  idVerification: null,
  currentStep: 1,
  isComplete: false,
};

interface KycContextType {
  kycState: KycFormState;
  updatePersonalInformation: (data: PersonalInformationForm) => void;
  updateResidentialInformation: (data: ResidentialInformationForm) => void;
  updateYourAssets: (data: YourAssetsForm) => void;
  updateIdVerification: (data: Partial<IdVerificationForm>) => void;
  submitKycData: () => Promise<boolean>;
  isSubmitting: boolean;
  resetKycData: () => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
}

const KycContext = createContext<KycContextType | undefined>(undefined);

export const KycProvider: React.FC<{children: ReactNode}> = ({children}) => {
  const [kycState, setKycState] = useState<KycFormState>(initialKycState);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {accessToken} = useSelector((state: RootState) => state.loan);

  // Update methods for each section
  const updatePersonalInformation = (data: PersonalInformationForm) => {
    setKycState((prev) => ({
      ...prev,
      personalInformation: data,
    }));
  };

  const updateResidentialInformation = (data: ResidentialInformationForm) => {
    setKycState((prev) => ({
      ...prev,
      residentialInformation: data,
    }));
  };

  const updateYourAssets = (data: YourAssetsForm) => {
    setKycState((prev) => ({
      ...prev,
      yourAssets: data,
    }));
  };

  const updateIdVerification = (data: Partial<IdVerificationForm>) => {
    setKycState((prev) => ({
      ...prev,
      idVerification: {
        ...prev.idVerification,
        ...data,
      } as IdVerificationForm,
    }));
  };

  // Step navigation
  const goToNextStep = () => {
    setKycState((prev) => ({
      ...prev,
      currentStep: Math.min(prev.currentStep + 1, 4),
    }));
  };

  const goToPreviousStep = () => {
    setKycState((prev) => ({
      ...prev,
      currentStep: Math.max(prev.currentStep - 1, 1),
    }));
  };

  // Reset KYC data
  const resetKycData = () => {
    setKycState(initialKycState);
  };

  // Validate personal information
  const validatePersonalInfo = (data: PersonalInformationForm | null): boolean => {
    if (!data) return false;

    return !!(
      data.firstName &&
      data.lastName &&
      data.dateOfBirth &&
      data.phoneNumber &&
      data.occupation &&
      data.occupationIndustry
    );
  };

  // Validate residential information
  const validateResidentialInfo = (data: ResidentialInformationForm | null): boolean => {
    if (!data) return false;

    return !!(
      data.streetAddress &&
      data.city &&
      data.stateProvince &&
      data.country &&
      data.postalCode &&
      data.citizenshipStatus
    );
  };

  // Validate assets information
  const validateAssetsInfo = (data: YourAssetsForm | null): boolean => {
    if (!data) return false;

    const isValid = !!(
      data.fundsSource &&
      data.annualIncome &&
      data.accountPurposes &&
      data.accountPurposes.length > 0 &&
      data.legalConfirmation &&
      data.platformDisclaimer
    );

    if (!isValid) {
      Alert.alert('Missing Information', 'All asset information fields are required.');
    }

    return isValid;
  };

  // Validate ID verification
  const validateIdVerification = (data: IdVerificationForm | null): boolean => {
    if (!data) return false;

    const isValid = !!(data.selfieImage && data.idFrontImage && data.proofOfAddressImage);

    if (!isValid) {
      Alert.alert(
        'Missing Information',
        'All required verification images must be provided.',
      );
    }

    return isValid;
  };

  // Prepare form data for submission
  const prepareFormData = (state: KycFormState): FormData => {
    const {personalInformation, residentialInformation, yourAssets, idVerification} =
      state;
    const formData = new FormData();

    // Transform data to match the expected backend structure
    const kycData = {
      personalInformation: {...personalInformation},
      residentialInformation: {...residentialInformation},
      yourAssets: {...yourAssets},
      idVerification: {
        idType: idVerification?.idType || 'national_id',
        idFrontImageURL: '',
        idBackImageURL: '',
        selfieImageURL: '',
        proofOfAddressImageURL: '',
      },
    };

    // Append the JSON data
    formData.append('formData', JSON.stringify(kycData));

    // Append images if they exist
    if (idVerification) {
      // Selfie image
      if (idVerification.selfieImage) {
        const fileName = idVerification.selfieImage.split('/').pop() || 'selfie.jpg';
        formData.append('selfieImage', {
          uri: idVerification.selfieImage,
          type: 'image/jpeg',
          name: fileName,
        } as any);
      }

      // ID front image
      if (idVerification.idFrontImage) {
        const fileName = idVerification.idFrontImage.split('/').pop() || 'id_front.jpg';
        formData.append('idFrontImage', {
          uri: idVerification.idFrontImage,
          type: 'image/jpeg',
          name: fileName,
        } as any);
      }

      // ID back image (optional)
      if (idVerification.idBackImage) {
        const fileName = idVerification.idBackImage.split('/').pop() || 'id_back.jpg';
        formData.append('idBackImage', {
          uri: idVerification.idBackImage,
          type: 'image/jpeg',
          name: fileName,
        } as any);
      }

      // Proof of address image
      if (idVerification.proofOfAddressImage) {
        const fileName =
          idVerification.proofOfAddressImage.split('/').pop() || 'address_proof.jpg';
        formData.append('proofOfAddressImage', {
          uri: idVerification.proofOfAddressImage,
          type: 'image/jpeg',
          name: fileName,
        } as any);
      }
    }

    return formData;
  };

  // Submit KYC data to backend
  const submitKycData = async (): Promise<boolean> => {
    // Validate all sections
    if (
      !validatePersonalInfo(kycState.personalInformation) ||
      !validateResidentialInfo(kycState.residentialInformation) ||
      !validateAssetsInfo(kycState.yourAssets) ||
      !validateIdVerification(kycState.idVerification)
    ) {
      return false;
    }

    try {
      setIsSubmitting(true);

      // Prepare form data
      const formData = prepareFormData(kycState);

      // Submit to backend
      const response = await AuthenticationInstance.post('/user/kyc-verify', formData, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      // Handle successful submission
      if (response.status === 200 || response.status === 201) {
        setKycState((prev) => ({
          ...prev,
          isComplete: true,
        }));

        navigationRef.navigate('BottomTabs', {
          screen: 'Loan',
          params: {screen: 'LoanApproval'},
        });

        return true;
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      // Extract error message if possible
      let errorMessage = 'An unknown error occurred during verification submission.';
      if (axios.isAxiosError(error) && error.response) {
        errorMessage = error.response.data?.message || errorMessage;
      }

      Alert.alert('Submission Failed', errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo<KycContextType>(
    () => ({
      kycState,
      updatePersonalInformation,
      updateResidentialInformation,
      updateYourAssets,
      updateIdVerification,
      submitKycData,
      isSubmitting,
      resetKycData,
      goToNextStep,
      goToPreviousStep,
    }),
    [kycState, isSubmitting],
  );

  return <KycContext.Provider value={value}>{children}</KycContext.Provider>;
};

// Custom hook for using the KYC context
export const useKyc = (): KycContextType => {
  const context = useContext(KycContext);
  if (context === undefined) {
    throw new Error('useKyc must be used within a KycProvider');
  }
  return context;
};
