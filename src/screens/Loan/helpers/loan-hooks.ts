import {useQuery} from '@tanstack/react-query';
// import {createLoanWebHub} from '../_web-flow_/LoanWebHub';
import {getUserLoans} from './loan-queries';

// export const useWebLoanHub = () => {
//   const navigation = useNavigation<StackNavigationProp<LoanStackParamList>>();
//   const dispatch = useAppDispatch();

//   return useMemo(() => createLoanWebHub({navigation, dispatch}), [navigation, dispatch]);
// };

export const useUserLoans = () => {
  const query = useQuery({
    queryKey: ['user-loans'],
    queryFn: getUserLoans,
  });
  return query;
};
