import {formatNumber} from '@/utils/index';

export const APR = 11.45;

export const calculateDailyInterest = (loanAmount: number, apr: number): number => {
  return (loanAmount * apr) / 365;
};

export const calculateMonthlyInterest = (loanAmount: number, apr: number): number => {
  return (loanAmount * apr) / 12;
};

export const calculateYearlyInterest = (loanAmount: number, apr: number): number => {
  return loanAmount * apr;
};

/**
 * Calculates interest based on payment frequency.
 */
export const calculateInterestByFrequency = (
  loanAmount: number,
  apr: number,
  frequency: string,
): number => {
  const normalizedFrequency = frequency.toLowerCase();

  switch (normalizedFrequency) {
    case 'daily':
      return calculateDailyInterest(loanAmount, apr);
    case 'monthly':
      return calculateMonthlyInterest(loanAmount, apr);
    case 'yearly':
      return calculateYearlyInterest(loanAmount, apr);
    default:
      return calculateMonthlyInterest(loanAmount, apr);
  }
};

export const calculateRequiredCollateral = (
  borrowAmount: number,
  tokenPrice: number,
  ltvRatio: number,
  currency: string = 'USD',
): string => {
  const adjustedTokenPrice = currency === 'EUR' ? tokenPrice * 0.92 : tokenPrice;
  const requiredCollateral = borrowAmount / (adjustedTokenPrice * ltvRatio);
  return requiredCollateral.toFixed(8);
};

export const calculateMaxLoanAmount = (
  collateralAmount: number,
  tokenPrice: number,
  ltvRatio: number,
  currency: string = 'USD',
): string => {
  const adjustedTokenPrice = currency === 'EUR' ? tokenPrice * 0.92 : tokenPrice;
  const maxLoan = collateralAmount * adjustedTokenPrice * ltvRatio;
  return maxLoan.toFixed(8);
};

/* ============================================================================================== */
/*                                              UTILS                                             */
/* ============================================================================================== */

/**
 * Format interest amount with currency
 */
export const formatInterestWithCurrency = (
  amount: number,
  currency: string,
  decimals: number = 2,
): string => {
  return `${formatNumber(amount, {decimals})} ${currency}`;
};

export const validateAmount = (value: string): boolean => {
  const cleanValue = value.replace(/,/g, '');
  const numValue = parseFloat(cleanValue);
  return !isNaN(numValue) && numValue > 0;
};
