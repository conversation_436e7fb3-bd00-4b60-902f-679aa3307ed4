import React, {memo, useEffect, useState} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';

import type {BottomSheetListData, CurrencyOption} from '@/components/BottomSheetList';
import MButton from '@/components/MButton';
import BottomSheetListModal from '@/components/modals/BottomSheetListModal';
import {useAppSelector, useLoan} from '@/hooks/redux';
import {useModals} from '@/hooks/useModals';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';
import LoanDetailsForm from '../../components/LoanDetailsForm';
import {calculateRequiredCollateral} from '../../helpers/loan-calculations';
import {DEFAULT_LOAN_DATA} from './utils';
import {capitalizeAll} from '@/utils';

const LoanDetails = () => {
  const tokenPrices = useAppSelector((state) => state.auth.tokenPrices);

  const [loanData, setLoanData] = useState(DEFAULT_LOAN_DATA);
  const [currentModalData, setCurrentModalData] = useState<any[]>([]);
  const [currentModalType, setCurrentModalType] = useState<string>('');
  const [currentModalTitle, setCurrentModalTitle] = useState<string>('');

  const {refs, show} = useModals(['list']);
  const {accessToken} = useLoan();

  const getTokenPrice = (tokenSymbol: string): number => {
    const basePrice =
      tokenSymbol === 'BTC' ? tokenPrices?.[0]?.[0] : tokenPrices?.[1]?.[0];
    return basePrice;
  };

  const calculateAmounts = () => {
    if (loanData.borrowAmount) {
      const tokenPrice = getTokenPrice(loanData.collateralCurrency.label);
      const borrowValue = parseFloat(loanData.borrowAmount);
      const ltvRatio = parseFloat(loanData.loanToValue) / 100;

      const requiredCollateral = calculateRequiredCollateral(
        borrowValue,
        tokenPrice,
        ltvRatio,
        loanData.borrowCurrency.label,
      );

      setLoanData((prev) => ({
        ...prev,
        collateralAmount: requiredCollateral.toString(),
      }));
    }
  };

  // New modal handler that works with existing data structure
  const handleOpenBottomSheet = (data: BottomSheetListData) => {
    setCurrentModalData(data.data);
    setCurrentModalType(data.type);
    setCurrentModalTitle(data.title ?? '');
    show('list');
  };

  // Handle selection from the modal
  const handleModalSelection = (item: any) => {
    if (currentModalType === 'currency') {
      const currencyItem = item as CurrencyOption;
      setLoanData((prev) => ({
        ...prev,
        [currencyItem.type === 'fiat' ? 'borrowCurrency' : 'collateralCurrency']:
          currencyItem,
      }));
    } else {
      if (item.label.includes('%')) {
        setLoanData((prev) => ({...prev, loanToValue: item.label}));
      } else {
        setLoanData((prev) => ({
          ...prev,
          [item.label.includes('month') ? 'term' : 'interestPayment']: item.label,
        }));
      }
    }

    setCurrentModalData([]);
    setCurrentModalType('');
  };

  const handleReviewPress = () => {
    navigateViaBottomTabs('Loan', 'LoanReview', loanData);
  };

  const isButtonDisabled = !loanData.borrowAmount;

  // Get current selected value for highlighting
  const getCurrentSelected = () => {
    if (currentModalType === 'currency') {
      // Determine if it's borrow or collateral currency
      const firstItem = currentModalData[0];
      if (firstItem?.type === 'fiat') {
        return loanData.borrowCurrency.label;
      } else {
        return loanData.collateralCurrency.label;
      }
    } else if (currentModalData[0]?.label?.includes('%')) {
      return loanData.loanToValue;
    } else if (currentModalData[0]?.label?.includes('month')) {
      return loanData.term;
    } else {
      return loanData.interestPayment;
    }
  };

  useEffect(() => {
    if (loanData.borrowAmount || loanData.collateralAmount) {
      calculateAmounts();
    }
  }, [loanData.borrowCurrency, loanData.collateralCurrency, loanData.loanToValue]);

  return (
    <View style={styles.root}>
      <ScrollView style={styles.content}>
        <LoanDetailsForm
          setLoanData={setLoanData}
          loanData={loanData}
          getTokenPrice={getTokenPrice}
          onOpenBottomSheet={handleOpenBottomSheet}
        />
      </ScrollView>

      <Footer style={styles.footer}>
        <MButton
          text="Review Loan"
          onPress={handleReviewPress}
          disabled={isButtonDisabled}
        />
      </Footer>

      <BottomSheetListModal
        ref={refs.list}
        data={currentModalData}
        onSelect={handleModalSelection}
        selectedValue={getCurrentSelected()}
        snapPoints={['40%']}
        title={currentModalTitle}
      />
    </View>
  );
};

export default memo(LoanDetails);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
  },
  footer: {
    marginBottom: 12,
  },
});
