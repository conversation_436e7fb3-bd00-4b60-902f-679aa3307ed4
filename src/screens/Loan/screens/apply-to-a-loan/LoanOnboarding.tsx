import React, {memo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import AssetifyLogo from '@/assets/logo/assetifyWithText.svg';
import CarouselNormal from '@/components/CarouselNormal';
import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {QUOTES} from './utils';

const LoanOnboarding = () => {
  const handleStartPress = () => {
    navigateViaBottomTabs('Loan', 'LoanRegister');
  };

  const logoWidth = theme.isSmallDevice ? '140px' : '150px';
  const logoHeight = theme.isSmallDevice ? '120px' : '140px';
  const logoStyle = {
    marginTop: theme.isSmallDevice ? 22 : 42,
    marginBottom: theme.isSmallDevice ? 34 : 40,
  };
  const quoteTextStyle = {
    fontSize: theme.isSmallDevice ? 22 : 24,
  };
  const quoteDescriptionTextStyle = {
    fontSize: theme.isSmallDevice ? 16 : 19,
  };

  const renderQuote = ({item}: {item; index: number}) => (
    <View style={styles.quoteContainer}>
      <Text style={[styles.quoteText, quoteTextStyle]}>{item.title}</Text>

      <Text style={[styles.descriptionText, quoteDescriptionTextStyle]}>
        {item.description}
      </Text>
    </View>
  );

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <AssetifyLogo width={logoWidth} height={logoHeight} style={logoStyle} />

        <View style={styles.carouselContainer}>
          <CarouselNormal data={QUOTES} renderItem={renderQuote} />
        </View>
      </View>

      <Footer style={styles.footer}>
        <MButton text="Start Your Loan Application" onPress={handleStartPress} />
      </Footer>
    </View>
  );
};

export default memo(LoanOnboarding);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    textAlign: 'center',
  },
  carouselContainer: {
    flex: 1,
    marginVertical: theme.layout.pv.lg,
  },
  quoteContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme.layout.ph.lg,
  },
  quoteText: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.base.black,
    textAlign: 'center',
    marginBottom: 16,
  },
  descriptionText: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.base.black,
    textAlign: 'center',
    opacity: 0.8,
  },
  footer: {
    marginBottom: 12,
  },
});
