import React, {memo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import LockIcon from '@/assets/icons/lock.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {setMpcComputedAddress} from '@/storage/actions/loanActions';

type LoanPaymentMethodsProps = {
  navigation?: any;
  ethAddress?: string;
  solAddress?: string;
  };

const LoanPaymentMethods: React.FC<LoanPaymentMethodsProps> = ({
  navigation,
  route,
}: any) => {
  const {ethAddress, btcAddress, chain, solAddress} = route.params;
  const dispatch = useAppDispatch();

  const handleAddNewCard = () => {
    // Temporary
    if (chain == 'BTC') {
      dispatch(setMpcComputedAddress(btcAddress));
    } else if (chain == 'SOL') {
      dispatch(setMpcComputedAddress(solAddress));
    } else {
      dispatch(setMpcComputedAddress(ethAddress));
    }

    navigation.navigate('LoanNewPaymentMethod');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Payment Methods</Text>
        <Text style={styles.description}>This is where you will receive your funds.</Text>
      </View>

      <View style={styles.separatorContainer}>
        <View style={styles.separator} />
      </View>

      <View style={styles.content}>
        <TouchableOpacity
          style={styles.addCardButton}
          onPress={handleAddNewCard}
          activeOpacity={0.7}
        >
          <View style={styles.plusIconContainer}>
            <Text style={styles.plusIcon}>+</Text>
          </View>
          <Text style={styles.addCardText}>Add new card</Text>
        </TouchableOpacity>

        <View style={styles.securityContainer}>
          <LockIcon width={16} height={16} color={GlobalStyles.gray.gray800} />
          <Text style={styles.securityText}>Encrypted and stored securely.</Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default memo(LoanPaymentMethods);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: GlobalStyles.base.black,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: GlobalStyles.gray.gray900,
    marginBottom: 8,
    lineHeight: 22,
  },
  separatorContainer: {
    width: '100%',
    paddingVertical: 8,
    backgroundColor: 'transparent',
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  separator: {
    height: 1,
    backgroundColor: '#E5E5E5',
    width: '100%',
  },
  addCardButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: GlobalStyles.gray.gray500,
    padding: 16,
    borderRadius: 12,
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
  },
  plusIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#666666',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  plusIcon: {
    color: GlobalStyles.base.white,
    fontSize: 20,
    fontWeight: '500',
    lineHeight: 22,
  },
  addCardText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  securityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 24,
    paddingHorizontal: 6,
    gap: 8,
  },
  securityText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
});
