import React, {memo, useCallback, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  View,
} from 'react-native';

import EmptyLoan from '@/assets/icons/emptyLoan.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {navigate} from '@/navigation/utils/navigation';
import LoanCard from '../../components/LoanCard';
import {useUserLoans} from '../../helpers/loan-hooks';
import {ILoanState, Loan} from '../../helpers/loan-types';

const EmptyLoanSvg = () => <EmptyLoan width={80} height={80} />;

const LoanActiveList = () => {
  const {data, isLoading, error, refetch} = useUserLoans();
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const activeLoans = useMemo(() => {
    if (!data) return [];

    const filtered = data.filter((loan: Loan) => loan.state === ILoanState.ACTIVE);

    return filtered.sort((a: Loan, b: Loan) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [data]);

  const handleLoanPress = useCallback((loanId: string) => {
    navigate('LoanDashboard', {loanId});
  }, []);

  const renderLoanItem = ({item}: {item: Loan}) => {
    return <LoanCard loan={item} onPress={handleLoanPress} />;
  };

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
        <Text style={styles.loadingText}>Loading active loans...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load loans. Please try again.</Text>
      </View>
    );
  }

  if (activeLoans.length === 0) {
    return (
      <View style={styles.root}>
        <View style={styles.centered}>
          <EmptyLoanSvg />
          <Text style={styles.emptyTitle}>No Active Loans</Text>
          <Text style={styles.emptyText}>You don't have any active loans.</Text>
        </View>
      </View>
    );
  }

  return (
    <FlatList
      data={activeLoans}
      keyExtractor={(item) => item._id}
      renderItem={renderLoanItem}
      contentContainerStyle={styles.listContainer}
      showsVerticalScrollIndicator={false}
      initialNumToRender={5}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor={GlobalStyles.primary.primary500}
          colors={[GlobalStyles.primary.primary500]}
        />
      }
    />
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  listContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  errorText: {
    fontSize: 16,
    color: GlobalStyles.error.error500,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: GlobalStyles.gray.gray700,
    textAlign: 'center',
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginTop: 16,
    marginBottom: 8,
  },
  debugText: {
    marginTop: 16,
    fontSize: 14,
    color: GlobalStyles.primary.primary500,
    textDecorationLine: 'underline',
  },
});

export default memo(LoanActiveList);
