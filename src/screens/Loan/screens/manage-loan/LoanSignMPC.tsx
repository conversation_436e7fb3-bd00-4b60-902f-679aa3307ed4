import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import AssetifyLogo from '@/assets/logo/Logo.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {SignatureGenerationService} from '@/mpc/ecdsa/signature-generation';
import {LOAN_SERVICE, SESSION_SERVICE_URL} from '@env';
import axios from 'axios';
import {useSelector} from 'react-redux';
import {DKGSession} from 'specs/common-payloads';
import {SignData} from 'specs/signature-payloads';
import Success from '@/screens/Success';
import {useAppSelector} from '@/hooks/redux';
import {networks, payments, Transaction} from 'bitcoinjs-lib';
import Big from 'big.js';
import {ethers} from 'ethers';
import {RootState} from '@/storage/store';
import {Transaction as SOLTransaction} from '@solana/web3.js';
import {SchnorrSignatureGenerationService} from '@/mpc/schnorr/signature-generation';

const LoanDoneSignMPC = ({navigation, route}: any) => {
  const {sessionID} = route.params;
  const [isSigningComplete, setIsSigningComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // const {accessToken} = useAppSelector((state) => state.loan);
  const {accessToken} = useSelector((state: any) => state.loan);
  // const keyshareFromStore = useSelector((state: RootState) => state.auth.keyshare);
  // console.log('keyshareFromStore', keyshareFromStore);

  const loanId = sessionID.replace('-DSG', '');

  const {
    loanData,
    activeLoans,
    // accessToken: loanAccessToken,
  } = useSelector((state: any) => state.loan);

  console.log('active loans', activeLoans);
  console.log('loanData', loanData);

  const player = activeLoans ? activeLoans[loanId] : null;

  console.log('active loans', activeLoans);
  console.log(loanData);

  useEffect(() => {
    const runMPCWithDelay = async () => {
      try {
        // console.log(`http://localhost:8000/api/v1/loan/${loanId}/sign-request/tx`);
        console.log(`${LOAN_SERVICE}/loan/${loanId}/sign-request/tx`);

        const getPayloadFromEndpoint = await axios.get(
          `${LOAN_SERVICE}/loan/${loanId}/sign-request/tx`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
        );

        console.log('getPayloadFromEndpoint', getPayloadFromEndpoint.data);

        // const getLoanUTXOs = await axios.get(`${LOAN_SERVICE}/loan/${loanId}`, {
        //   headers: {
        //     Authorization: `Bearer ${accessToken}`,
        //   },
        // });

        // let utxos = getLoanUTXOs.data?.loan?.utxos?.utxos || undefined;
        let utxos = undefined;

        const {payout} = (await getPayloadFromEndpoint).data.tx;

        if (utxos) {
          const decodedTx = Transaction.fromHex(payout.txSerialized);
          console.log(decodedTx);

          const sighashes = decodedTx.ins.map((input, index) => {
            const utxo = utxos![index]!;
            console.log('CURRENT UTXO', utxo);
            const inputValue = Big(utxo.value).mul(100000000).toNumber();
            console.log(`Converted Value (satoshis): ${inputValue}`);

            // Build script needed for sighash
            const p2pkhPayment = payments.p2pkh({
              pubkey: Buffer.from(player.pk, 'hex'),
              network: networks.bitcoin,
            });
            const witnessScript = p2pkhPayment.output;

            const sighash = decodedTx.hashForWitnessV0(
              index,
              witnessScript!,
              inputValue,
              Transaction.SIGHASH_ALL,
            );

            console.log(`Sighash #${index}: ${sighash.toString('hex')}`);
            return sighash;
          });

          // 2. Break the entire array of sighashes into small batches
          const batchSize = 1; // how many to sign concurrently in each batch
          let sigHashesSigned: {index: number; sig: string}[] = [];

          for (let start = 0; start < sighashes.length; start += batchSize) {
            // Extract this batch’s slice of inputs
            const batch = sighashes.slice(start, start + batchSize);

            // Sign them in parallel—but only for this small batch
            const batchResults = await Promise.all(
              batch.map(async (sighash, iWithinBatch) => {
                let signData: SignData = {
                  sign_id: Array(32).fill(0),
                  counterparties: [2],
                  message_hash: Array.from(sighash),
                };

                const signatureGenerationservice = new SignatureGenerationService();

                const session = {
                  session: {
                    parameters: {threshold: 2, share_count: 3},
                    party_index: 1,
                    session_id: Array(32).fill(0),
                  },
                } as DKGSession;

                const networkParams = {
                  socketURL: `${SESSION_SERVICE_URL}`,
                  namespace: 'mpc-connections',
                  sessionId: sighash.toString('hex'),
                };

                await signatureGenerationservice.startDsgSession(
                  session,
                  {
                    signData: signData,
                    party: player,
                  },
                  networkParams,
                  payout.txSerialized,
                  'BTC',
                );

                // const sig = await signatureGenerationservice.runSigningPhases(signData, txSerialized);
                // const rBuf = Buffer.from(sig.phase3.x_coord, 'hex');
                // const sBuf = Buffer.from(sig.phase4.signature, 'hex');
                // if (rBuf.length !== 32 || sBuf.length !== 32) {
                //   throw new Error('Invalid signature length');
                // }

                // // Convert raw 64-byte signature to DER encoding
                // const rawSig64 = Buffer.concat([rBuf, sBuf]);
                // const sig2DER = script.signature.encode(rawSig64, Transaction.SIGHASH_ALL);

                // console.log(`DER Encoded Signature: ${sig2DER.toString('hex')}`);

                return {
                  index: start + iWithinBatch,
                  sig: 'test',
                };
              }),
            );

            // Accumulate into our final array
            sigHashesSigned = sigHashesSigned.concat(batchResults);
          }

          setIsSigningComplete(true);
        } else if (loanData.currency === 'ETH') {
          const txs = ethers.Transaction.from(JSON.parse(payout.txSerialized));

          const signData = {
            sign_id: Array(32).fill(0),
            counterparties: [2],
            message_hash: Array.from(Buffer.from(txs.unsignedHash.slice(2), 'hex')),
          } as SignData;

          const networkParams = {
            socketURL: `${SESSION_SERVICE_URL}`,
            namespace: 'mpc-connections',
            sessionId: sessionID,
          };

          const signatureGenerationservice = new SignatureGenerationService();

          const session = {
            session: {
              parameters: {threshold: 2, share_count: 3},
              party_index: 1,
              session_id: Array(32).fill(0),
            },
          } as DKGSession;

          await signatureGenerationservice.startDsgSession(
            session,
            {
              signData: signData,
              party: player,
            },
            networkParams,
            txs.unsignedSerialized,
            'ETH',
          );

          setIsSigningComplete(true);
        } else if (loanData.collateralCurrency === 'SOL') {
          console.log('SOL');
          // console.log(getPayloadFromEndpoint.data.tx.payout)
          // const tx = getPayloadFromEndpoint.data.tx;

          // const parsedTx = JSON.parse(getPayloadFromEndpoint.data);

          // console.log('parsedTx', parsedTx);

          // const transactionData = JSON.parse(parsedTx.tx);

          const msgHex = JSON.parse(getPayloadFromEndpoint.data.tx.payout.txSerialized);

          // console.log('tx', tx);
          console.log('msgHex', msgHex);

          const sessionData = {
            session: {
              parameters: {threshold: 2, share_count: 3},
              party_index: 0,
              session_id: Array(32).fill(0),
            },
          } as DKGSession;
          const signData = {
            sign_id: Array(32).fill(0),
            counterparties: [1],
            message_hash: Array.from(Buffer.from(msgHex.messageHex, 'hex')),
          } as SignData;

          const networkParams = {
            socketURL: `${SESSION_SERVICE_URL}`,
            namespace: 'schnorr-connections',
            sessionId: sessionID,
          };

          console.log('player', player);
          const signatureGenerationservice = new SchnorrSignatureGenerationService(
            sessionData,
            signData,
            player,
          );

          const sig = await signatureGenerationservice.startDsgSession(
            sessionData,
            {signData, party: player},
            networkParams,
            sessionID,
            'SOL',
          );

          console.log('sig', sig);

          setIsSigningComplete(true);
        }
      } catch (err) {
        console.error('DKG error:', err);
        setError(err instanceof Error ? err.message : 'An error occurred during signing');
      }
    };

    runMPCWithDelay();
  }, [sessionID, navigation, accessToken, loanId, player]);

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={[styles.title, {color: 'red'}]}>Error</Text>
        <Text style={styles.description}>{error}</Text>
      </View>
    );
  }

  if (isSigningComplete) {
    return (
      <Success
        title="Success"
        description="Your transaction has been signed"
        isSuccess={true}
        onFinish={() =>
          navigation.reset('BottomTabs', {
            screen: 'Wallet',
            params: {screen: 'WalletHome'},
          })
        }
      />
    );
  }

  return (
    <View style={styles.container}>
      <AssetifyLogo width={90} height={90} style={styles.logo} />
      <Text style={styles.title}>Signing... </Text>
      <Text style={styles.description}>
        Signing Wallet Transaction for Collateral Return
      </Text>
    </View>
  );
};

export default LoanDoneSignMPC;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
    paddingBottom: 22,
  },
  logo: {
    marginBottom: 22,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    textAlign: 'center',
    paddingHorizontal: 60,
    marginBottom: 14,
  },
  description: {
    color: GlobalStyles.gray.gray800,
    fontSize: 18,
    textAlign: 'center',
    paddingHorizontal: 70,
    lineHeight: 26,
  },
});
