import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useSelector} from 'react-redux';

import HoldToConfirmButton from '@/components/HoldToConfirmButton';
import SafeAreaInset from '@/components/SafeAreaInset';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAuth} from '@/hooks/redux';
import {formatNumber} from '@/utils/index';

const LoanConfirmSignMPC = ({route, navigation}: any) => {
  const {sessionID} = route.params;
  const {loanData} = useSelector((state: any) => state.loan);
  console.log('loanData', loanData);
  const {userAddresses} = useAuth();

  const handleConfirm = async () => {
    navigation.navigate('LoanSignMPC', {
      sessionID,
    });
    return true;
  };

  return (
    <View style={{flex: 1, backgroundColor: GlobalStyles.base.white}}>
      <SafeAreaInset type="top" />

      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>Confirm Transaction</Text>

          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>Important Information</Text>
            <Text style={styles.infoText}>
              You are about to sign a transaction that will return your collateral. This
              action cannot be undone. Please review all details carefully before
              proceeding.
            </Text>
          </View>

          <View style={styles.detailsContainer}>
            <Text style={styles.sectionTitle}>Transaction Details</Text>

            <View style={styles.detailRow}>
              <Text style={styles.label}>Amount to Sign:</Text>
              <Text style={styles.value}>
                {formatNumber(loanData?.collateralAmount, {
                  decimals: 6,
                  prefix: loanData?.collateralCurrency == 'BTC' ? 'BTC ' : loanData?.collateralCurrency == 'SOL' ? 'SOL ' : 'ETH ',
                })}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.label}>Payout Address:</Text>
              <Text style={styles.value} numberOfLines={1} ellipsizeMode="middle">
                {loanData?.collateralCurrency == 'BTC'
                  ? userAddresses[0].address
                  : loanData?.collateralCurrency == 'SOL'
                    ? loanData?.mpcComputedAddress
                    : userAddresses[1].address || '0x...'}
              </Text>
            </View>

            <Text style={styles.note}>
              Note: To change the payout address, please contact Assetify support team
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <View style={styles.confirmButtonContainer}>
            <HoldToConfirmButton onPress={handleConfirm} />
          </View>
        </View>
      </View>

      <SafeAreaInset type="bottom" />
    </View>
  );
};

export default LoanConfirmSignMPC;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 24,
    color: GlobalStyles.base.black,
  },
  infoBox: {
    backgroundColor: GlobalStyles.gray.gray500,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
    lineHeight: 20,
  },
  detailsContainer: {
    backgroundColor: GlobalStyles.gray.gray50,
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: GlobalStyles.gray.gray900,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  label: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  value: {
    fontSize: 14,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
    maxWidth: '60%',
  },
  note: {
    fontSize: 16,
    color: GlobalStyles.gray.gray700,
    fontStyle: 'italic',
    marginTop: 12,
    textAlign: 'center',
  },
  footer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  cancelButton: {
    paddingVertical: 12,
    marginBottom: 16,
  },
  cancelButtonText: {
    textAlign: 'center',
    color: GlobalStyles.gray.gray600,
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButtonContainer: {
    alignItems: 'center',
  },
});
