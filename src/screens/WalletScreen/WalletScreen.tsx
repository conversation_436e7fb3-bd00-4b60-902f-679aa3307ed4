import analytics from '@react-native-firebase/analytics';
import {useFocusEffect} from '@react-navigation/native';
import i18next from 'i18next';
import React, {memo, useCallback, useEffect, useState} from 'react';
import {initReactI18next, useTranslation} from 'react-i18next';
import {Dimensions, RefreshControl, ScrollView, StyleSheet, View} from 'react-native';

import {ChainToTokens} from '@/constants/Chains';
import GlobalStyles from '@/constants/GlobalStyles';
import {languageResources} from '@/constants/i18next';
import {useDebounce} from '@/hooks';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {useAppDispatch, useAuth, useCommon} from '@/hooks/redux';
import {navigate} from '@/navigation/utils/navigation';
import {
  setAvalancheFixed,
  setUser,
  setUserAddresses,
} from '@/storage/actions/authActions';
import CarouselParallax from '@components/CarouselParallax';
import LoadingHandler from '@components/LoadingHandler';
import SafeAreaInset from '@components/SafeAreaInset';
import SendReceiveButtons from '@components/SendReceiveButtons/SendReceiveButtons';
import theme from '@styles/theme';
import AssetList from './components/AssetList/AssetList';
import TopNavigation from './components/TopNavigation/TopNavigation';
import WalletBalance from './components/WalletBalance/WalletBalance';
import {useSecondaryAssets} from './hooks/useSecondaryAssets';
import {useWalletData} from './hooks/useWalletData';
import {
  checkNotifSubscription,
  checkSolanaWallet,
  checkWallets,
} from './walletScreenUtils';
import {setIsRegistered} from '@/storage/actions/loanActions';

const WalletScreen = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {open: openSheet, close: closeSheet} = useBottomSheet('wallet');

  const {
    assets: authAssets,
    calculatedPrices: authCalculatedPrices,
    stableCoins: authStableCoins,
    tokenPrices: authTokenPrices,
    walletBalance: authWalletBalance,
    userAddresses,
    user,
    avalancheFixed,
  } = useAuth();

  const {currency, language} = useCommon();

  const walletData = useWalletData(
    userAddresses[0]?.address,
    currency,
    authAssets,
    authCalculatedPrices,
    authStableCoins,
    authTokenPrices,
    authWalletBalance,
  );

  const secondaryAssets = useSecondaryAssets(user, userAddresses);

  const debouncedLoading = useDebounce(
    walletData.loading || secondaryAssets.loading,
    500,
  );

  const [blur, setBlur] = useState(false);

  const handleMenuPress = useCallback(() => {
    analytics().logEvent('Home_infoButton_tap');
    navigate('Info');
  }, []);

  const handleSettingsPress = useCallback(() => {
    analytics().logEvent('Home_settingsButton_tap');
    navigate('Settings');
  }, []);

  const handleNotificationsPress = useCallback(() => {
    navigate('Notifications');
  }, []);

  const handleSendPress = useCallback(() => {
    analytics().logEvent('Home_sendButton_tap');
    navigate('BottomTabs', {
      screen: 'Wallet',
      params: {
        screen: 'SendAssetInput',
        params: {
          assets: walletData.assets,
          tokenPrices: walletData.tokenPrices,
          calculatedPrices: walletData.calculatedPrices,
          stableCoins: walletData.stableCoins,
        },
      },
    });
  }, [
    walletData.assets,
    walletData.tokenPrices,
    walletData.calculatedPrices,
    walletData.stableCoins,
  ]);

  const handleReceivePress = useCallback(() => {
    analytics().logEvent('Home_receiveButton_tap');

    navigate('BottomTabs', {
      screen: 'Wallet',
      params: {
        screen: 'ReceiveAsset',
        params: {
          assets: walletData.assets,
          tokenPrices: walletData.tokenPrices,
        },
      },
    });
  }, [walletData.assets, walletData.tokenPrices]);

  const handleAssetPress = useCallback(
    (asset: any) => {
      console.log('asset', asset);

      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.blockchain);

      const addressFix =
        asset.blockchain === 'kaspa'
          ? userAddresses[index]?.addresses[0]
          : userAddresses[index]?.address.includes('bitcoincash:')
          ? userAddresses[index]?.address.split('bitcoincash:')[1]
          : userAddresses[index]?.address;

      console.log('INDEX >>>>', index);
      console.log('addresses', addressFix);
      if (!addressFix) return;

      navigate('CurrencySpecific', {
        asset,
        stableCoin: '',
        stableCoinIndex: 0,
        stableCoinAmount: '0.00',
        address: addressFix,
        calculatedPrices: walletData.calculatedPrices[index] || [],
        tokenPrices: walletData.tokenPrices[index] || [],
      });
    },
    [walletData.assets, walletData.tokenPrices, walletData.calculatedPrices],
  );

  const handleStableCoinPress = useCallback(
    (asset: any, stableCoin: string) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.title);
      const stableCoinIndex =
        ChainToTokens[asset.title]?.findIndex((t) => t.tokenSymbol === stableCoin) + 1;

      if (!stableCoinIndex) return;

      const amount = walletData.stableCoins[index]?.[stableCoinIndex - 1] ?? '0';
      console.log('amount', amount);
      if (
        !amount ||
        !walletData.calculatedPrices[index] ||
        !walletData.tokenPrices[index]
      ) {
        return;
      }

      navigate('CurrencySpecific', {
        asset,
        stableCoin,
        stableCoinIndex,
        stableCoinAmount: amount,
        address: userAddresses[index],
        calculatedPrices: walletData.calculatedPrices[index] || [],
        tokenPrices: walletData.tokenPrices[index] || [],
      });
    },
    [walletData.assets, walletData.tokenPrices, walletData.calculatedPrices],
  );

  const handleBuyPress = useCallback(() => {
    analytics().logEvent('Home_buyButton_tap');
    openSheet({
      type: 'wallet',
      assets: walletData.assets,
      calculatedPrices: walletData.calculatedPrices,
      tokenPrices: walletData.tokenPrices,
      stableCoins: walletData.stableCoins,
      handleAssetPress: (asset) => {
        handleRampAssetPress(asset);
        closeSheet();
      },
      handleStableCoinPress: (asset, stableCoin) => {
        handleRampStableCoinPress(asset, stableCoin);
        closeSheet();
      },
      blur,
      setBlur,
      currency,
    });
  }, [
    walletData.assets,
    walletData.tokenPrices,
    walletData.calculatedPrices,
    walletData.stableCoins,
  ]);

  const handleRampAssetPress = useCallback(
    (asset: any) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.blockchain);
      navigate('RampDetails', {
        currency: asset.title,
        currencySymbol: asset.tokenSymbol,
        tokenPrice: walletData.tokenPrices[index]?.[0] || [],
        addressToUse: userAddresses[index]?.address,
      });
    },
    [walletData.tokenPrices, userAddresses, user.wallet],
  );

  const handleRampStableCoinPress = useCallback(
    (asset: any, stableCoin: string) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.title);
      const normalizedStableCoin = stableCoin === 'USDT20' ? 'USDT' : stableCoin;
      const stableCoinIndex =
        ChainToTokens[asset.title]?.findIndex(
          (t) => t.tokenSymbol === normalizedStableCoin,
        ) + 1;

      if (!stableCoinIndex) return;

      navigate('RampDetails', {
        currency: normalizedStableCoin,
        currencySymbol: normalizedStableCoin,
        tokenPrice: walletData.tokenPrices[index]?.[stableCoinIndex],
        addressToUse: userAddresses[index]?.address,
        blockchainSymbol: asset.tokenSymbol,
      });
    },
    [walletData.tokenPrices, userAddresses, user.wallet],
  );

  useFocusEffect(
    useCallback(() => {
      // setSentryContext(userAddresses[0]?.address);

      // dispatch(setAccessToken(''));
      // dispatch(setIsRegistered(false));

      const initializeWallet = async () => {
        if (user !== null && !avalancheFixed) {
          const check = await checkWallets(user.wallet, userAddresses);
          if (check !== null) {
            dispatch(setUser({...user, wallet: check.wallets}));
            dispatch(setUserAddresses(check.addresses));
            dispatch(setAvalancheFixed());
            walletData.refreshData();
          } else if (!avalancheFixed) {
            dispatch(setAvalancheFixed());
          }
        }

        analytics().logEvent('Home_screen_open');
        checkNotifSubscription(userAddresses[0]?.address);
        walletData.refreshData();

        i18next.use(initReactI18next).init({
          compatibilityJSON: 'v3',
          lng: language,
          fallbackLng: 'en',
          resources: languageResources,
        });

        const availableAssets = await secondaryAssets.getAvailableSecondaryAssets();
        console.log('availableAssets', availableAssets);
        if (availableAssets.length > 0) {
          const success = await secondaryAssets.createSecondaryWallets(availableAssets);
          if (success) {
            walletData.refreshData();
          }
        }

        const result = await checkSolanaWallet(user.wallet, userAddresses);
        if (result !== null) {
          dispatch(setUserAddresses(result.addresses));
          dispatch(setUser({...user, wallet: result.wallets}));
        }
      };

      initializeWallet();
    }, []),
  );

  useEffect(() => {
    walletData.setLoading(true);
  }, [currency]);

  if (debouncedLoading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <LoadingHandler />
      </View>
    );
  }

  return (
    <>
      <SafeAreaInset type="top" />

      <TopNavigation
        handleMenu={handleMenuPress}
        handleNotifications={handleNotificationsPress}
        handleSettings={handleSettingsPress}
        title={t('wallet.title')}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={walletData.refreshing}
            onRefresh={walletData.onRefresh}
            tintColor={GlobalStyles.primary.primary500}
            colors={[GlobalStyles.primary.primary500]}
          />
        }
      >
        <View style={styles.carouselContainer}>
          <CarouselParallax
            images={[
              {
                link: 'Subscribe',
                title: 'Need a Loan?',
                subtitle: 'Get a crypto-backed fiat loan in 10 minutes',
              },
              {
                link: 'Subscribe',
                title: 'Your Crypto, Simplified',
                subtitle: 'Secure, flexible, and tailored to you',
              },
            ]}
            height={200}
          />
        </View>

        <View style={styles.balance}>
          <WalletBalance balance={walletData.balance} blur={blur} currency={currency} />

          <SendReceiveButtons
            handleSendPress={handleSendPress}
            handleReceivePress={handleReceivePress}
            handleBuyPress={handleBuyPress}
            buyButtonTitle="Buy / Sell"
          />
        </View>

        <View style={styles.assetsContainer}>
          <AssetList
            assets={walletData.assets}
            blur={blur}
            setBlur={setBlur}
            tokenPrices={walletData.tokenPrices}
            handleAssetPress={handleAssetPress}
            handleStableCoinPress={handleStableCoinPress}
            currency={currency}
          />
        </View>
      </ScrollView>
    </>
  );
};

export default memo(WalletScreen);

const styles = StyleSheet.create({
  assetsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    display: 'flex',
    alignSelf: 'center',
    width: '90%',
    paddingBottom: theme.spacing.lg,
  },
  balance: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 10,
    overflow: 'hidden',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    display: 'flex',
    width: '90%',
    elevation: 4,
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  bottomContainer: {
    backgroundColor: '#e6e6e6',
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 0.5,
    position: 'absolute',
    bottom: 0,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    borderTopRightRadius: 18,
    borderTopLeftRadius: 18,
    // Shadows
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 5,
  },
  titleNewsContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    paddingHorizontal: 8,
    height: Dimensions.get('window').width * 0.41,
    marginVertical: 10,

    width: Dimensions.get('window').width * 0.9,
  },
  titleNewsImage: {
    alignSelf: 'center',
    borderRadius: 8,
  },
  titleNewsTextContainer: {
    display: 'flex',
    flex: 1,
    justifyContent: 'flex-end',
    alignSelf: 'flex-end',
    alignItems: 'flex-start',
    paddingHorizontal: 10,
    paddingTop: 10,
    width: '80%',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 8,
    bottom: 16,
  },
  titleNewsTitle: {
    fontSize: 22,
    lineHeight: 22,
    fontWeight: '600',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontStyle: 'normal',
    top: 24,
    color: GlobalStyles.base.white,
    paddingLeft: 6,
  },
  carouselContainer: {
    marginBottom: 18,
    width: '100%',
    paddingHorizontal: 0,
    alignItems: 'center',
  },
});
