import {combineReducers} from 'redux';

import uiReducer from '../slices/ui';
import authReducer from './authReducer';
import commonReducer from './commonReducer';
import exchangeReducer from './exchangeReducer';
import lightningReducer from './lightningReducer';
import loanReducer from './loanReducer';

const appReducer = combineReducers({
  ui: uiReducer,
  auth: authReducer,
  common: commonReducer,
  lightning: lightningReducer,
  exchange: exchangeReducer,
  loan: loanReducer,
});

const rootReducer = (state: any, action: any) => {
  if (action.type === 'CLEAR_STORE') {
    return {
      ui: {
        list: {
          isOpen: false,
          data: null,
          snapPoints: 0,
          enableSearch: false,
        },
        wallet: {
          isOpen: false,
          data: null,
          snapPoints: 0,
          enableSearch: false,
        },
      },
      common: {
        language: 'en',
        currency: 'USD',
      },
      auth: {
        isLoggedIn: false,
        user: null,
        userAddresses: [],
        walletTxs: [],
        calculatedPrices: [],
        tokenPrices: [],
        assets: [],
        stableCoins: [],
        walletBalance: 0,
        avalancheFixed: false,
        featuresFlag: false,
        keyshare: null,
      },
      lightning: {
        workingDir: null,
        modalCount: 0,
        hideModalPermanently: false,
        webSocket: null,
        balanceMsat: 0,
        refundablesLength: 0,
      },
      exchange: {
        payinAmount: 0,
        payoutAmount: 0,
        rate: 0,
        rateID: '',
        timeLeft: 0,
        isPaused: false,
      },
      loan: {
        loanData: {
          borrowAmount: '',
          borrowCurrency: 'USD',
          collateralAmount: '',
          collateralCurrency: 'BTC',
          loanToValue: '50%',
          interestPayment: 'Monthly',
          term: '12 months',
          id: '',
        },
        accessToken: null,
        refreshToken: null,
        isRegistered: false,
        tokenTimestamp: null,
        hasLoan: false,
        mpcComputedAddress: null,
        activeLoans: null,
      },
    };
  }
  return appReducer(state, action);
};

export default rootReducer;
