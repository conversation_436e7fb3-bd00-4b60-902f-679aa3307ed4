import {configureStore} from '@reduxjs/toolkit';
import {persistReducer, persistStore} from 'redux-persist';
import { AuthState } from '@/types/authTypes';

import {reduxStorage} from '.';
import rootReducer from './reducers';

const persistConfig = {
  key: 'root',
  storage: reduxStorage,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      immutableCheck: false,
      serializableCheck: false,
    }),
});

export type RootState = {
  auth: AuthState;
  // ... other state types
};
export type AppDispatch = typeof store.dispatch;

export const persistor = persistStore(store);
