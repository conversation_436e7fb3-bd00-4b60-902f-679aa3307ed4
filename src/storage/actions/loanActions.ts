import {Party } from 'specs/keygen-payloads';
import { SchnorrParty } from 'specs/schnorr-payloads';

export const setLoanData = (loanData: {
  borrowAmount: string;
  borrowCurrency: string;
  collateralAmount: string;
  collateralCurrency: string;
  loanToValue: string;
  interestPayment: string;
  term: string;
  id?: string;
}) => ({
  type: 'SET_LOAN_DATA',
  payload: loanData,
});

export const setAccessToken = (accessToken: string | null) => ({
  type: 'SET_ACCESS_TOKEN',
  payload: accessToken,
});

// Action to add or update a loan in the activeLoans map
export const addLoan = (loanID: string, party?: Party | SchnorrParty) => ({
  type: 'ADD_LOAN',
  payload: {loanID, party},
});

// Action to remove a loan from the activeLoans map
export const removeLoan = (loanID: string) => ({
  type: 'REMOVE_LOAN',
  payload: {loanID},
});

export const setRefreshToken = (refreshToken: string | null) => ({
  type: 'SET_REFRESH_TOKEN',
  payload: refreshToken,
});

export const setIsRegistered = (isRegistered: boolean) => ({
  type: 'SET_IS_REGISTERED',
  payload: isRegistered,
});

export const setTokenTimestamp = (timestamp: number | null) => ({
  type: 'SET_TOKEN_TIMESTAMP',
  payload: timestamp,
});

export const setHasLoan = (hasLoan: boolean) => ({
  type: 'SET_HAS_LOAN',
  payload: hasLoan,
});

export const setMpcComputedAddress = (ethAddress: string) => ({
  type: 'SET_MPC_COMPUTED_ADDRESS',
  payload: ethAddress,
});
