export const changeLanguage = (language: string) => {
  return {
    type: 'CHANGE_LANGUAGE',
    payload: language,
  };
};

export const changeCurrency = (currency: string) => {
  return {
    type: 'CHANGE_CURRENCY',
    payload: currency,
  };
};

export const setSeedPhraseConfirmed = (seedPhraseConfirmed: number) => {
  return {
    type: 'SET_SEED_PHRASE_CONFIRMED',
    payload: seedPhraseConfirmed,
  };
};
