export type AuthUserZPub = {
  accountXpriv: string;
  accountXpub: string;
  derivationPath: string;
  derivationType: string;
  rootKey: string;
};

export type AuthUserWallet = {
  blockchain: string;
  network: string;
  mnemonic: string;
  seed: string;
  zPub: AuthUserZPub;
  address?: string;
  privateKey?: string;
  publicKey?: string;
};

export type NativeUserWallet = {
  address?: string;
  blockchain: string;
  mnemonic: string;
  network: string;
  privateKey?: string;
  publicKey?: string;
  seed: string;
  xPubsList: Array<AuthUserZPub>;
};

export type AuthUser = {
  wallet: Array<AuthUserWallet>;
  pinCode: string;
};

export type AuthAddress = {
  address: string;
  privateKey: string;
  publicKey: string;
  chain: string | null;
  qrCode?: string;
};

export type AuthKaspaAddress = {
  addresses: Array<AuthAddress>;
  chain: string;
};

export type AuthAddressType = AuthKaspaAddress | AuthAddress;

export type AuthAddresses = Array<AuthAddressType>;

export type AuthCalculatedPrices = string[][];
export type AuthTokenPrices = string[][];
export type AuthStableCoins = string[][];

export type AuthAsset = {
  title: string;
  amount: string;
  tokenLogo: string;
  tokenSymbol: string;
  blockchain: string;
  network: string;
};

export type AuthAssets = Array<AuthAsset>;

export type AuthTransaction = {
  timestamp: number;
  _id: string;
  transactionId: string;
  chain: string;
  wallet: string;
  chainData: string;
  status: string;
  tokensTransfered: {
    confirmedBalance: string;
    contractAddress: string;
    name: string;
    recipientAddress: string;
    senderAddress: string;
    symbol: string;
    tokenDecimals: string;
    txid: string;
    wallet: string;
  }[];
  fee: {
    amount: string;
    unit: string;
  };
  recipients: [
    {
      address: string;
      amount: string;
    },
  ];
  senders: [
    {
      address: string;
      amount: string;
    },
  ];
  sentOrReceived: 'sent' | 'received';
};

export type AuthWalletTxs = {
  [key: string]: AuthTransaction[];
};

export type AuthState = {
  isLoggedIn: boolean;
  user: AuthUser | null;
  userAddresses: AuthAddresses;
  walletTxs: AuthWalletTxs;
  calculatedPrices: AuthCalculatedPrices;
  tokenPrices: AuthTokenPrices;
  assets: AuthAssets;
  stableCoins: AuthStableCoins;
  walletBalance: number;
  avalancheFixed: boolean;
  isAuthenticated: boolean;
  showAuthScreen: boolean;
  biometricsInProgress: boolean;
  keyshare: any | null;
};
