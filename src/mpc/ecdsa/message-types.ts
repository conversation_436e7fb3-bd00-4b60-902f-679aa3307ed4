export enum MessageType {
  Broadcast = 'broadcast',
  ReadyToConfirm = 'readyToConfirm',
  AllPlayersReady = 'allPlayersReady',

  ReceiveFragment = 'receiveFragment',
  ReceiveZeroSharePhase2to4 = 'receiveZeroSharePhase2to4',
  ReceiveZeroSharePhase3to4 = 'receiveZeroSharePhase3to4',
  ReceiveMulSharePhase3to4 = 'receiveMulSharePhase3to4',
  SignPhase1to2 = 'sendSignPhase1to2',
  SignPhase2to3 = 'sendSignPhase2to3',
  BroadcastPhase3 = 'broadcastPhase3',
  BroadcastSignPhase3 = 'broadcastSignPhase3',
  BroadcastProofCommitment = 'broadcastProofCommitment',
}

export enum BroadcastType {
  BroadcastDerivationPhase2to4 = 'BroadcastDerivationPhase2to4',
  BroadcastDerivationPhase3to4 = 'BroadcastDerivationPhase3to4',
  BroadcastProofCommitment = 'BroadcastProofCommitment',
}
