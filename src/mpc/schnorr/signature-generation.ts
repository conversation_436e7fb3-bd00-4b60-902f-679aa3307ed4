// dsg.service.ts

import {Party} from '../../../specs/keygen-payloads';
import {MPCClientService} from '../common/mpc-client';
import {ethers} from 'ethers';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {
  SignPhase1Output,
  SignPhase2Output,
  SignPhase3Output,
  SignPhase2Input,
  SignPhase3Input,
  SignPhase4Input,
  SignPhase4Output,
} from '../../../specs/NativeSampleModule';
import {WebSocketTransmitMessage} from '../../../specs/network-payloads';
import {
  TransmitPhase1to2,
  TransmitPhase2to3,
  Broadcast3to4,
  SignData,
} from '../../../specs/signature-payloads';
import {registerDsgEvents} from './base-events';
import {DKGSession} from 'specs/common-payloads';
import { networks, payments, script, Transaction } from 'bitcoinjs-lib';
import Big from 'big.js'
  import { SchnorrParty, SchnorrSignRound0Input, SchnorrSignRound0Output, SchnorrSignRound1Input, SchnorrSignRound1Output, SchnorrSignRound2Input, SchnorrSignRound2Output, SchnorrSignRound3Input, SchnorrSignRound3Output } from 'specs/schnorr-payloads';
import { SchnorrKeyGenerationService } from './key-generation';

export interface IUTXO {
  scriptPubKey: {
      addresses: string[],
      asm: string,
      hex: string,
      type: string,
  },

  prevTxId: string,
  index: number,
  value: string,
  isSpent: boolean,
}
export class SchnorrSignatureGenerationService extends MPCClientService {
  // DSG-specific fields
  private sessionData?: DKGSession;

  private signStateId?: number;
  private signature?: string;
  private signingParties: number[] = [];

  public signMessages: {
    round1: (string | undefined)[];
    round2: (string | undefined)[];
    round3: (string | undefined)[];
  };

  private signData?: SignData;

  private keyshare: SchnorrParty;


  constructor(sessionData: DKGSession, signData: SignData, keyshare: SchnorrParty) {
    super();
    this.sessionData = sessionData;
    this.signMessages = {
      round1: Array(this.getShareCount()).fill(undefined),
      round2: Array(this.getShareCount()).fill(undefined),
      round3: Array(this.getShareCount()).fill(undefined),
    }
    this.signingParties = signData.counterparties;
    
    this.keyshare = keyshare;
  }

  
  // A promise that will resolve when signing is complete.
  // A resolver function for the promise.

  /**
   * Start the DSG session
   */
  async startDsgSession(
    sessionData: DKGSession,
    session: {signData: SignData; party: SchnorrParty},
    networkParams: {socketURL: string; namespace: string; sessionId: string},
    txSerialized: string,
    chain: 'BTC' | 'ETH' | 'SOL',

    utxos?: IUTXO[],
  ) {
    await this.initializeBaseSocket(networkParams, false);

    // Create a promise and capture its resolve function.

    this.sessionData = sessionData;
    this.signData = session.signData;
    
    console.log('signData WE HAVE', this.signData)

    const partyIndex = this.getPartyIndex();
    this.signingParties = [this.getPartyIndex(), ...this.signData.counterparties].sort((a, b) => a - b);
    console.log(`Party ${this.getPartyIndex()}: Signing with parties: ${this.signingParties.join(', ')}`);

    this.emitWithSession('register', {partyIndex, dsg: true, chain});
    console.log(`DSG session started with ID: ${networkParams.sessionId}`);
    await this.waitForEvent('allPlayersReady', () => true, 3.6e6);
    console.log(`Party ${this.getPartyIndex()}: All signing parties ready.`);

    console.log('signData', this.signData)
    const messageHex = Buffer.from(this.signData?.message_hash || (this.signData as any).message_hash).toString('hex');
    console.log('messageHex', messageHex)
    // const messageHex = Buffer.from('8001000103292e7c74ba9b3c558a15a2f88a02f82e8d2f59b71a32d337d8458b4cd2c3c57129a80a4b240dfe23b27d6ca1f4b47d1249232283f26e891ff848c9692d724cfe0000000000000000000000000000000000000000000000000000000000000000844287c16af5bc3b4debf887a7f1d868b3ad896d7afa4fd7a3a7f794c81a3ca601020200010c0200000040420f000000000000', 'hex').toString('hex');
    const derivationPath = "m";

    try {
      await this.processRound1(messageHex,derivationPath);
      await this.waitForEvent<{ round: string }>('proceedToNextRound', (data) => data.round === 'dsg_r1');
      console.log(`Party ${this.getPartyIndex()}: DSG Round 1 barrier passed.`);

      await this.processRound2();

      console.log('round2 passed')
      await this.waitForEvent<{ round: string }>('proceedToNextRound', (data) => data.round === 'dsg_r2');
      console.log(`Party ${this.getPartyIndex()}: DSG Round 2 barrier passed.`);

      await this.processRound3();
      await this.waitForEvent<{ round: string }>('proceedToNextRound', (data) => data.round === 'dsg_r3');
      console.log(`Party ${this.getPartyIndex()}: DSG Round 3 barrier passed.`);
      
      await this.finalizeSignature();

      if (!this.signature) throw new Error("Signature generation failed: signature is undefined.");
      return { signature: this.signature };

    } catch (error) {
      console.error(`Party ${this.getPartyIndex()}: Error during signature generation:`, error);
      throw error;
    }


  }


  /**
   * Let `dsg.events.ts` set up all socket event handlers
   */
  public registerChildEvents(): void {
    registerDsgEvents(this);
  }

  public resetState(): void {
    console.log('Resetting DSG signing state...');

  }

  // -----------------------------
  // PHASE METHODS
  // -----------------------------

  private async processRound1(messageHex: string, derivationPath: string): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Processing Sign Round 1`);
    const input: SchnorrSignRound0Input = { keyshare_json: JSON.stringify(this.keyshare), message_hex: messageHex, deriv_path: derivationPath };

    console.log('input', input)
    const outStr = await DKGHandler.schnorrSignStart(JSON.stringify(input));
    const output = JSON.parse(outStr) as SchnorrSignRound0Output;
     

    if (typeof output === 'string' || !output || (output as any).error) throw new Error(`DSG Round 1 native call failed: ${JSON.stringify(output)}`);

    // **FIX 1: SET STATE ID FIRST**
    this.signStateId = +output.party_state_id;

    // Put our own message into the queue, then signal.
    this.handleSignRound1Data({ senderIndex: this.getPartyIndex(), payload: output.broadcast_message });

    // NOW send to others.

    console.log('signingParties', this.signingParties)
    for (const party of this.signingParties) {
      if (party !== this.getPartyIndex()) {
        console.log(`${this.getPartyIndex()}: sending to ${party}`)
        this.emitWithSession('sendP2P', { senderIndex: this.getPartyIndex(), receiverIndex: party, payload: output.broadcast_message, eventName: 'DSG_ReceiveDataPhase1', sessionId: this.sessionIDWS });
      }
    }
  } 

  private async processRound2(): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Processing Sign Round 2`);
    const messages = this.signingParties.map(idx => this.signMessages.round1[idx]!);
    const input: SchnorrSignRound1Input = { party_state_id: this.signStateId!, messages };

    console.log('input', input)
    console.log('messages', messages)
    const outStr = await DKGHandler.schnorrSignR1(JSON.stringify(input));

    console.log('outStr[IMP]', outStr)
    const output = JSON.parse(outStr) as SchnorrSignRound1Output;
    if (typeof output === 'string' || !output || (output as any).error) throw new Error(`DSG Round 2 native call failed: ${JSON.stringify(output)}`);
    
    // **FIX 2: SET STATE ID FIRST**
    this.signStateId = +output.party_state_id;
    
    this.handleSignRound2Data({ senderIndex: this.getPartyIndex(), payload: output.broadcast_message });

    for (const party of this.signingParties) {
      if (party !== this.getPartyIndex()) {
        this.emitWithSession('sendP2P', { senderIndex: this.getPartyIndex(), receiverIndex: party, payload: output.broadcast_message, eventName: 'DSG_ReceiveDataPhase2', sessionId: this.sessionIDWS });
      }
    }
  }

  private async processRound3(): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Processing Sign Round 3`);
    const messages = this.signingParties.map(idx => this.signMessages.round2[idx]!);
    const input: SchnorrSignRound2Input = { party_state_id: this.signStateId!, messages };

    console.log('input', input)
    console.log('messages', messages)
    const outStr = await DKGHandler.schnorrSignR2(JSON.stringify(input));

    console.log('outStr', outStr)
    const output = JSON.parse(outStr) as SchnorrSignRound2Output;
    if (typeof output === 'string' || !output || (output as any).error) throw new Error(`DSG Round 3 native call failed: ${JSON.stringify(output)}`);
    
    // **FIX 3: SET STATE ID FIRST**
    this.signStateId = +output.party_state_id;

    this.handleSignRound3Data({ senderIndex: this.getPartyIndex(), payload: output.broadcast_message });

    for (const party of this.signingParties) {
        if (party !== this.getPartyIndex()) {
            this.emitWithSession('sendP2P', { senderIndex: this.getPartyIndex(), receiverIndex: party, payload: output.broadcast_message, eventName: 'DSG_ReceiveDataPhase3', sessionId: this.sessionIDWS });
        }
    }
  }

  private async finalizeSignature(): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Finalizing signature`);
    const messages = this.signingParties.map(idx => this.signMessages.round3[idx]!);
    const input: SchnorrSignRound3Input = { party_state_id: this.signStateId!, messages };

    const outStr = await DKGHandler.schnorrSignR3(JSON.stringify(input));
    const output = JSON.parse(outStr) as SchnorrSignRound3Output;
    if (typeof output === 'string' || !output || (output as any).error) throw new Error(`DSG Finalization failed: ${JSON.stringify(output)}`);

    this.signature = output.signature;
    console.log(`✔ Party ${this.getPartyIndex()}: Signature complete!`);
  }



  // -----------------------------
  // HANDLERS
  // -----------------------------

  public handleSignRound1Data(data: { senderIndex: number; payload: string }): void {
    if (this.signMessages.round1[data.senderIndex] !== undefined) return;
    this.signMessages.round1[data.senderIndex] = data.payload;
    this.checkAndSignalRoundCompletion('dsg_r1');
  }

  public handleSignRound2Data(data: { senderIndex: number; payload: string }): void {
    if (this.signMessages.round2[data.senderIndex] !== undefined) return;
    console.log('data', data)
    console.log(`from: ${data.senderIndex} setting round2 [${data.senderIndex}] to ${data.payload}`)
    this.signMessages.round2[data.senderIndex] = data.payload;
    this.checkAndSignalRoundCompletion('dsg_r2');
  }

  public handleSignRound3Data(data: { senderIndex: number; payload: string }): void {
    if (this.signMessages.round3[data.senderIndex] !== undefined) return;
    this.signMessages.round3[data.senderIndex] = data.payload;
    this.checkAndSignalRoundCompletion('dsg_r3');
  }
  private checkAndSignalRoundCompletion(round: 'dsg_r1' | 'dsg_r2' | 'dsg_r3') {
    const isComplete = (arr: any[]) => this.signingParties.every(partyIdx => arr[partyIdx] !== undefined);
    
    let allMessagesIn = false;
    switch(round) {
      case 'dsg_r1': allMessagesIn = isComplete(this.signMessages.round1); break;
      case 'dsg_r2': allMessagesIn = isComplete(this.signMessages.round2); break;
      case 'dsg_r3': allMessagesIn = isComplete(this.signMessages.round3); break;
    }
      
    if (allMessagesIn) {
      console.log(`Party ${this.getPartyIndex()}: All messages for round '${round}' received. Notifying server.`);
      this.emitWithSession('roundComplete', { partyIndex: this.getPartyIndex(), round });
    }
  }

  public getPartyIndex(): number {
    // console.log('sessionData', this.sessionData?.session.party_index)
    // if (!this.sessionData?.session.party_index) {
      // throw new Error('Party index not set');
    // }
    return this.sessionData!.session.party_index;
  }
  public getShareCount(): number {
    if (!this.sessionData?.session.parameters.share_count) {
      throw new Error('Share count not set');
    }
    return this.sessionData?.session.parameters.share_count;
  }
  public getThreshold(): number {
    if (!this.sessionData?.session.parameters.threshold) {
      throw new Error('Threshold not set');
    }
    return this.sessionData?.session.parameters.threshold;
  }

  public emitWithSession(event: string, data: any): void {
    console.log(event, data);
    this.socket.emit(event, {sessionId: this.sessionIDWS, ...data});
  }
}
