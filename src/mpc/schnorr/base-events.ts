import { MPCClientService } from "../common/mpc-client";
import { SchnorrKeyGenerationService } from "./key-generation";
import { SchnorrSignatureGenerationService } from "./signature-generation";

export function registerBaseMpcEvents(service: MPCClientService) {
  const { socket } = service;

  // "resetState" event
  socket.on('resetState', () => {
    console.info('info', 'Received resetState. Resetting state.');
    service.resetState();
  });

  // "readyToConfirm" event
  socket.once('readyToConfirm', (dsg: boolean, signWithAssetify: boolean) => {
    console.info('info', `Received "readyToConfirm" with dsg=${dsg}`);
    const partyIndex = service.getPartyIndex();
    console.log('party index', partyIndex)
    service.emitWithSession('confirmReady', { partyIndex, dsg: dsg, signWithAssetify, sessionId: service.sessionIDWS });
  });


}

// DKG Events
export function registerDkgEvents(service: SchnorrKeyGenerationService) {
const { socket } = service;


socket.on('allPlayersReady', (dsg: boolean) => {
  console.info('info', `Received "allPlayersReady"`);
});
// These listeners now ONLY pass data to the service handlers.
// All logic is removed from here.
socket.on('DKG_ReceivePublicKey', (data: { senderIndex: number; payload: string }) => {
  service.handleReceivedDKG_PublicKey({ partyIndex: data.senderIndex, publicKey: data.payload });
});

socket.on('DKG_ReceiveDataPhase1', (data: { senderIndex: number; payload: string }) => {
  service.handleReceivedDkgRound1Data(data);
});

socket.on('DKG_ReceiveDataPhase2', (data: { senderIndex: number; payload: string }) => {
  service.handleReceivedDkgRound2Data(data);
});
}

  // socket.on('allDKG_PublicKeyReceived', (data: { partyIndex: number, publicKey: string }) => {
  //   console.info(`Received DKG Public Key complete event from Party ${data.partyIndex}`);
  //   service.handleReceivedDKG_PublicKey({ partyIndex: data.partyIndex, publicKey: data.publicKey });
  // });

  // socket.on('allDkgRound0DataReceived', (data: { senderIndex: number, payload: string }) => {
  //   console.info(`Received DKG Round 0 data complete event from Party ${data.senderIndex}`);
  //   service.handleReceivedDkgRound0Data({ senderIndex: data.senderIndex, payload: data.payload });
  // });

  // socket.on('allDkgRound1DataReceived', (data: { senderIndex: number, payload: string }) => {
  //   console.info(`Received DKG Round 1 data complete event from Party ${data.senderIndex}`);
  //   service.handleReceivedDkgRound1Data({ senderIndex: data.senderIndex, payload: data.payload });
  // });

  // socket.on('allDkgRound2DataReceived', (data: { senderIndex: number, payload: string }) => {
  //   console.info(`Received DKG Round 2 data complete event from Party ${data.senderIndex}`);
  //   service.handleReceivedDkgRound2Data({ senderIndex: data.senderIndex, payload: data.payload });
  // });

  export function registerDsgEvents(service: SchnorrSignatureGenerationService) {
    const { socket } = service;

    socket.on('allPlayersReady', async (dsg: boolean, signWithAssetify: boolean) => {
      if (dsg) {
        console.info(`Party ${service.getPartyIndex()}: All players ready for schnorr signature generation`);
      }
    });
  socket.on('DSG_ReceiveDataPhase1', (data: { senderIndex: number, payload: string }) => {
    console.log('DSG_ReceiveDataPhase1', data)
    service.handleSignRound1Data(data);
  });

  socket.on('DSG_ReceiveDataPhase2', (data: { senderIndex: number, payload: string }) => {
    console.log('DSG_ReceiveDataPhase2', data)
    service.handleSignRound2Data(data);
  });

  socket.on('DSG_ReceiveDataPhase3', (data: { senderIndex: number, payload: string }) => {
    console.log('DSG_ReceiveDataPhase3', data)
    service.handleSignRound3Data(data);
  });
}
// DSG (Distributed Signature Generation) Events
// export function registerDsgEvents(service: SchnorrSignatureGenerationService) {
//   const { socket } = service;

//   socket.on('allPlayersReady', async (dsg: boolean, signWithAssetify: boolean) => {
//     if (dsg) {
//       console.info(`Party ${service.getPartyIndex()}: All players ready for schnorr signature generation`);
//     }
//   });
// socket.on('DSG_ReceiveDataPhase1', (data: { senderIndex: number, payload: string }) => {
//   console.log('DSG_ReceiveDataPhase1', data)
//   service.handleSignRound1Data(data);
// });

// socket.on('DSG_ReceiveDataPhase2', (data: { senderIndex: number, payload: string }) => {
//   console.log('DSG_ReceiveDataPhase2', data)
//   service.handleSignRound2Data(data);
// });

// socket.on('DSG_ReceiveDataPhase3', (data: { senderIndex: number, payload: string }) => {
//   console.log('DSG_ReceiveDataPhase3', data)
//   service.handleSignRound3Data(data);
// });



  // socket.on('allDkgRound2DataReceived', (data: { partyIndex: number }) => {
  // The actual event handlers for signature rounds are registered directly in the SchnorrSignatureGenerationService class
// }
