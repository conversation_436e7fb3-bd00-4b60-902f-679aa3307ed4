// dkg.service.ts

import { MPCClientService } from "../common/mpc-client";
import { DKGSession } from "specs/common-payloads";
import { PartyEncryptionKey, SchnorrDkgRound0Input, SchnorrDkgRound0Output, SchnorrDkgRound1Input, SchnorrDkgRound1Output, SchnorrDkgRound2Input, SchnorrDkgRound2Output, SchnorrParty } from "specs/schnorr-payloads";
import { registerDkgEvents } from "./base-events";
import DKGHandler from "../../../specs/NativeSampleModule";
import { PublicKey } from "@solana/web3.js";


export class SchnorrKeyGenerationService extends MPCClientService {

  public party?: SchnorrParty;
    private stateId?: number;
    private keyshare?: any;
    private encryptionSecret?: string;
  
    public partyPubList: (PartyEncryptionKey | undefined)[];
    public dkgMessages: { r1: (string | undefined)[]; r2: (string | undefined)[] };

    private sessionData!: DKGSession;

    constructor(sessionData: DKGSession) {
        console.log('sessionData', sessionData);
        console.log('SchnorrKeyGenerationService constructor');
        super();
        this.sessionData = sessionData;

        const n = this.getShareCount();
        this.partyPubList = Array(n).fill(undefined)
        this.dkgMessages = {
            r1: Array(n).fill(undefined),
            r2: Array(n).fill(undefined)
        }
    }

    public emitWithSession(event: string, data: any): void {
        this.socket.emit(event, data);
    }

    public getPartyIndex(): number {
        return this.sessionData.session.party_index;
    }

    public getShareCount(): number {
        return this.sessionData.session.parameters.share_count;
    }

    public getThreshold(): number {
        return this.sessionData.session.parameters.threshold;
    }

  // DKG-Specific Fields
  // public party?: SchnorrParty;



  /**
   * Start the DKG session
   */
  async startDkgSession(session: DKGSession, networkParams: { socketURL: string, namespace: string, sessionId: string}): Promise<SchnorrParty> {
    console.log('Starting DKG session');
    await this.initializeBaseSocket(networkParams, false);
    console.log('Initialized base socket');
    this.sessionData = session;
    this.sessionIDWS = networkParams.sessionId;
    const partyIndex = this.getPartyIndex();

    this.emitWithSession('register', { 
      sessionId: this.sessionIDWS,
      partyIndex, dsg: false,
      totalParties: this.getShareCount() });
    console.log(`DKG session started with ID: ${networkParams.sessionId}`);

    await this.waitForEvent('allPlayersReady', () => true, 3.6e6);
    console.log(`Party ${this.getPartyIndex()}: All players ready, proceeding with DKG`);

    // --- Round: Encryption Key Exchange ---
    await this.processEncryptionKeysExchange();
    await this.waitForEvent<{ round: string }>('proceedToNextRound', (data) => data.round === 'dkg_pk_exchange');
    console.log(`Party ${this.getPartyIndex()}: PK exchange barrier passed.`);

    // --- Round 1 ---
    await this.processRound1();
    await this.waitForEvent<{ round: string }>('proceedToNextRound', (data) => data.round === 'dkg_r1');
    console.log(`Party ${this.getPartyIndex()}: Round 1 barrier passed.`);

    // --- Round 2 ---
    await this.processRound2();
    await this.waitForEvent<{ round: string }>('proceedToNextRound', (data) => data.round === 'dkg_r2');
    console.log(`Party ${this.getPartyIndex()}: Round 2 barrier passed.`);

    // --- Round 3 (Final) ---
    await this.processRound3();
    if (!this.keyshare) throw new Error('DKG process failed - no keyshare generated');
    console.log(`Party ${this.getPartyIndex()}: DKG successfully completed.`);



    const address = new PublicKey(this.keyshare.public_key).toBase58()


    this.emitWithSession('done', {
      partyIndex: this.getPartyIndex(),
      SOLDerivedAddress: address,
      sessionId: this.sessionIDWS
    });

    this.party = {
      ...this.keyshare,
      session_id_ws: this.sessionIDWS,
      solana_address: address
    }

    return {
      ...this.keyshare,
      session_id_ws: this.sessionIDWS,
      solana_address: address
    }

  }


  /**
   * Our child-specific event registration is now delegated
   * to the `registerDkgEvents` function.
   */
  public registerChildEvents(): void {
    console.log('registering child events')
    registerDkgEvents(this);
  }


  public resetState(): void {
    console.log(`Party ${this.getPartyIndex()}: Resetting state.`);
    this.stateId = undefined;
    this.keyshare = undefined;
    this.encryptionSecret = undefined;
    this.partyPubList = Array(this.getShareCount()).fill(undefined);
    this.dkgMessages = {
      r1: Array(this.getShareCount()).fill(undefined),
      r2: Array(this.getShareCount()).fill(undefined),
    };
  }



  public async processRound1(): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Processing DKG Round 1`);
    const input: SchnorrDkgRound0Input = { t: this.getThreshold(), n: this.getShareCount(), party_id: this.getPartyIndex(), decryption_key: this.encryptionSecret!, all_parties_keys: this.partyPubList as PartyEncryptionKey[] };

    const outStr = await DKGHandler.schnorrProcessR1(JSON.stringify(input));
    const output = JSON.parse(outStr) as SchnorrDkgRound0Output;
    if (typeof output === 'string' || !output || (output as any).error) throw new Error(`DKG Round 1 failed: ${JSON.stringify(output)}`);
    
    this.stateId = +output.party_state_id;
    this.handleReceivedDkgRound1Data({ senderIndex: this.getPartyIndex(), payload: output.broadcast_message });

    for (let to = 0; to < this.getShareCount(); to++) {
      if (to !== this.getPartyIndex()) {
        console.log('sending P2p', {
            senderIndex: this.getPartyIndex(),
            receiverIndex: to,
            payload: output.broadcast_message,
            eventName: 'DKG_ReceiveDataPhase1',
            sessionId: this.sessionIDWS
        })
        this.emitWithSession('sendP2P', { senderIndex: this.getPartyIndex(), receiverIndex: to, payload: output.broadcast_message, eventName: 'DKG_ReceiveDataPhase1', sessionId: this.sessionIDWS });
      }
    }
  }

  private async processRound2(): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Processing DKG Round 2`);
    const input: SchnorrDkgRound1Input = { party_state_id: this.stateId!, messages: this.dkgMessages.r1 as string[] };

    const outStr = await DKGHandler.schnorrProcessR2(JSON.stringify(input));
    const output = JSON.parse(outStr) as SchnorrDkgRound1Output;
    if (typeof output === 'string' || !output || (output as any).error) throw new Error(`DKG Round 2 failed: ${JSON.stringify(output)}`);

    this.stateId = +output.party_state_id;
    this.handleReceivedDkgRound2Data({ senderIndex: this.getPartyIndex(), payload: output.broadcast_message });

    for (let to = 0; to < this.getShareCount(); to++) {
      if (to !== this.getPartyIndex()) {
        this.emitWithSession('sendP2P', { senderIndex: this.getPartyIndex(), receiverIndex: to,  payload: output.broadcast_message, eventName: 'DKG_ReceiveDataPhase2', sessionId: this.sessionIDWS });
      }
    }
  }


  private async processRound3(): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Processing DKG Round 3 (Final)`);
    const input: SchnorrDkgRound2Input = { party_state_id: this.stateId!, messages: this.dkgMessages.r2 as string[] };
    
    const outStr = await DKGHandler.schnorrProcessR3(JSON.stringify(input));
    const output = JSON.parse(outStr) as SchnorrDkgRound2Output;
    if (typeof output === 'string' || !output || (output as any).error) throw new Error(`DKG Round 3 failed: ${JSON.stringify(output)}`);

    this.keyshare = output.keyshare;
  }


  public async processEncryptionKeysExchange(): Promise<void> {
    console.log(`Party ${this.getPartyIndex()}: Processing Encryption Keys Exchange`);
    const encryptionSecret = DKGHandler.schnorrGenerateSk()!;
    this.encryptionSecret = encryptionSecret;
    const encryptionPublic = DKGHandler.schnorrGetPk(encryptionSecret)!;

    this.handleReceivedDKG_PublicKey({ partyIndex: this.getPartyIndex(), publicKey: encryptionPublic });

    for (let to = 0; to < this.getShareCount(); to++) {
      if (to !== this.getPartyIndex()) {
        console.log('sending P2p', {
            senderIndex: this.getPartyIndex(),
            receiverIndex: to,
            payload: encryptionPublic,
            eventName: 'DKG_ReceivePublicKey',
            sessionId: this.sessionIDWS
        })
        this.emitWithSession('sendP2P', {
            senderIndex: this.getPartyIndex(),
            receiverIndex: to,
            payload: encryptionPublic,
            eventName: 'DKG_ReceivePublicKey',
            sessionId: this.sessionIDWS
        });
      }
    }
  }
  private checkAndSignalRoundCompletion(round: 'dkg_pk_exchange' | 'dkg_r1' | 'dkg_r2') {
    const isComplete = (arr: any[]) => arr.every(item => item !== undefined);
    let allMessagesIn = false;

    switch(round) {
        case 'dkg_pk_exchange': allMessagesIn = isComplete(this.partyPubList); break;
        case 'dkg_r1': allMessagesIn = isComplete(this.dkgMessages.r1); break;
        case 'dkg_r2': allMessagesIn = isComplete(this.dkgMessages.r2); break;
    }
      
    if (allMessagesIn) {
        console.log(`plist`, this.partyPubList)
      console.log(`Party ${this.getPartyIndex()}: All messages for round '${round}' received. Notifying server.`);
      this.emitWithSession('roundComplete', { partyIndex: this.getPartyIndex(), round, sessionId: this.sessionIDWS });
    }
  }


  public handleReceivedDKG_PublicKey(data: { partyIndex: number; publicKey: string }) {
    if (this.partyPubList[data.partyIndex] !== undefined) return;
    this.partyPubList[data.partyIndex] = { party_id: data.partyIndex, public_key: data.publicKey };
    this.checkAndSignalRoundCompletion('dkg_pk_exchange');
  }

  public handleReceivedDkgRound1Data(data: { senderIndex: number; payload: string }) {
    if (this.dkgMessages.r1[data.senderIndex] !== undefined) return;
    this.dkgMessages.r1[data.senderIndex] = data.payload;
    this.checkAndSignalRoundCompletion('dkg_r1');
  }

  public handleReceivedDkgRound2Data(data: { senderIndex: number; payload: string }) {
    if (this.dkgMessages.r2[data.senderIndex] !== undefined) return;
    this.dkgMessages.r2[data.senderIndex] = data.payload;
    this.checkAndSignalRoundCompletion('dkg_r2');
  }

  

}
