// base-mpc.service.ts

import {io, Socket} from 'socket.io-client';
import {registerBaseMpcEvents} from '../ecdsa/base-events';
import { registerBaseMpcEvents as registerBaseMpcEventsSchnorr } from '../schnorr/base-events';

export abstract class MPCClientService {
  public socket!: Socket;
  public sessionIDWS!: string;

  async onModuleDestroy() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }

  constructor() {
    console.log('MPCClientService constructor');
  }

  /**
   * Initialize the underlying socket and call event registrations.
   */
  public async initializeBaseSocket(networkParams: {
    socketURL: string;
    namespace: string;
    sessionId: string;
  }, ecdsa: boolean = true): Promise<void> {
    this.sessionIDWS = networkParams.sessionId;
    const namespace = ecdsa ? '/mpc-connections' : '/schnorr-connections';

    console.log(`Connecting socket to: ${networkParams.socketURL}${namespace}`);
    this.socket = io(`${networkParams.socketURL}${namespace}`, {
      transports: ['websocket'],
      secure: false,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      auth: {
        sessionId: this.sessionIDWS,
      }
    });

    this.socket.onAny((event, ...args) => {
      console.log(event)
      console.log(`Got event: ${event} with args: ${args}`);
    });

    this.socket.on('connect', () => {
      console.log(`Socket connected to: ${this.socket.io}`);
    });

    this.socket.on('connect_error', (error) => {
      console.log(error);
      console.log(`Socket connection error: ${error.message}`);
      // this.error(`Socket connection error: ${error.message}`);
    });

    this.socket.on('disconnect', (reason) => {
      console.log(`Socket disconnected: ${reason}`);
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`Reconnection attempt #${attemptNumber}`);
    });

    this.socket.connect();

    console.log(`Socket connected for session: ${networkParams.sessionId}`);

    // Register the base events...
    // registerBaseMpcEvents(this);
    registerBaseMpcEventsSchnorr(this);

    // ...then let the child class register its own events
    this.registerChildEvents();
  }

  /** Common events that both DKG and DSG might share */
  // (No socket.on() calls here anymore; they move to base-mpc.events.ts)

  /**
   * Events that should be registered in the child (DKG/DSG) classes.
   */
  public abstract registerChildEvents(): void;

  /**
   * Override in child to define how that service resets internal state.
   */
  public abstract resetState(): void;

  /**
   * Common utility for waiting on an event with a certain condition.
   */
  public waitForEvent<T>(
    eventName: string,
    condition: (data: T) => boolean,
    timeoutMs: number = 100000,
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Timeout waiting for event: ${eventName}`));
      }, timeoutMs);

      const handler = (data: T) => {
        if (condition(data)) {
          clearTimeout(timeout);
          this.socket.off(eventName, handler);
          resolve(data);
        }
      };

      this.socket.on(eventName, handler);
    });
  }

  /**
   * Emit an event with the sessionId automatically appended.
   */

  public abstract emitWithSession(event: string, data: any): void;

  public abstract getPartyIndex(): number;

  public abstract getShareCount(): number;

  public abstract getThreshold(): number;
}
