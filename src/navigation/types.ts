import {InputType} from '@breeztech/react-native-breez-sdk';
import type {BottomTabScreenProps as RNBottomTabScreenProps} from '@react-navigation/bottom-tabs';
import type {CompositeScreenProps, NavigatorScreenParams} from '@react-navigation/native';
import type {StackScreenProps} from '@react-navigation/stack';

import type {CurrencyOption} from '@/components/BottomSheetList';
import {
  AuthAddressType,
  AuthAsset,
  AuthAssets,
  AuthCalculatedPrices,
  AuthStableCoins,
  AuthTokenPrices,
} from '@/types/authTypes';

export type ReceiveAssetParams = {
  asset: AuthAsset | null;
  stableCoin: string | null;
  stableCoinIndex: number | null;
  stableCoinAmount: string | null;
  address: any | null;
  assets: AuthAsset[] | null;
  tokenPrices: string | string[];
  calculatedPrices: string | string[];
};

export type OnboardingStackParamList = {
  OnboardingHome: undefined;

  NewWalletCreating: undefined;
  NewWallet: {
    loading?: boolean;
  };
  VerifyImport: {
    shuffledPhrases: string[];
    seedPhrase: string;
  };
  ImportNewWallet: undefined;

  Keychain: undefined;

  ImportWalletOptions: undefined;
  ImportWallet: {
    loading?: boolean;
  };

  RestoreWalletCheck: undefined;
  RestoreWalletListAvailable: undefined;
  RestoreRecoveryAndImport: {
    recoveryId?: string;
  };
};

export type WalletStackParamList = {
  WalletHome: undefined;
  CurrencySpecific: {};
  ReceiveAsset: ReceiveAssetParams;
  SendAssetInput: {
    assets: AuthAssets;
    tokenPrices: AuthTokenPrices;
    calculatedPrices: AuthCalculatedPrices;
    stableCoins: AuthStableCoins;
  };
  SendAssetConfirmation: {
    recipientAddress: string;
    amount: string;
    stableCoin: string;
    asset: AuthAsset;
    assets: AuthAsset[] | null;
    calculatedAmount: string;
    calculatedFee: string;
    tronFee: string;
    approximateFee: string;
    address: AuthAddressType | null;
    calculatedPrices: string[][];
    tokenPrices: string[] | string[][];
    stableCoinIndex: number;
    stableCoinAmount: string;
  };
  TxDetails: undefined;
  Subscribe: undefined;
  RampDetails: {
    currency: string;
    currencySymbol: string;
    tokenPrice: string;
    addressToUse: string;
  };
  RampConfirm: {
    currencyFrom: string;
    currencyTo: string;
    amount: string;
    location: string;
    action: string;
    addressToUse: string;
    state: string | null;
  };
};

export type LightningStackParamList = {
  LightningHome: undefined;
  LightningSend: {
    input?: InputType;
    lnurl?: string;
    bolt11?: string;
    amount?: number;
  };
  LightningReceive: undefined;
  LightningSwaps: undefined;
  LightningRefunds: undefined;
  LightningSwapOutConfirmation: undefined;
};

export type ExchangeStackParamList = {
  ExchangeHome: undefined;
  ExchangeConfirm: {
    payinLabel: CurrencyOption;
    payoutLabel: CurrencyOption;
  };
};

export type LoanStackParamList = {
  LoanOnboarding: undefined;
  LoanDetails: undefined;
  LoanReview: {
    borrowAmount: string;
    borrowCurrency: {
      fullName: string;
      label: string;
      type: string;
    };
    collateralAmount: string;
    collateralCurrency: {
      fullName: string;
      label: string;
      type: string;
    };
    loanToValue: string;
    interestPayment: string;
    term: string;
  };
  LoanLogin: undefined;
  LoanRegister: undefined;
  LoanRegistrationSuccess: undefined;
  LoanSettings: undefined;
  LoanConfirmation: undefined;
  LoanKYC: undefined;
  LoanList: undefined;
  LoanDashboard: undefined;
  LoanSignMPC: {
    loanID?: string;
  };
  LoanConfirmSignMPC: {
    loanID?: string;
  };

  WebLoanLogin: undefined;
  WebLoanRegister: {
    loanData?: Record<string, unknown>;
  };
  WebLoanConfirm: undefined;
  LoanCompleteWebFlow: undefined;

  LoanApproval: undefined;
  LoanMpc: {
    loanID: string;
  };
  LoanPaymentMethods: undefined;
  LoanNewPaymentMethod: undefined;
  LoanCollateral: undefined;

  WebLoanDashboard: undefined;
};

export type KeychainStackParamList = {
  KeychainHome: undefined;
  KeychainWalletLabel: {
    bottomTabsPresent: boolean;
  };
  KeychainPassword: {
    walletLabel: string;
    bottomTabsPresent: boolean;
  };
  KeychainConfirmPassword: {
    password: string;
    walletLabel: string;
    bottomTabsPresent: boolean;
  };
};

export type SettingsStackParamList = {
  SettingsHome: undefined;
  SeedPhrase: undefined;
  VerifySeedPhrase: undefined;
  PrivateKeys: undefined;
  PrivateKey: undefined;
  Keychain: NavigatorScreenParams<KeychainStackParamList>;
  TermsOfService: undefined;
  SecureStorage: undefined;
};

export type BottomTabParamList = {
  Onboarding: NavigatorScreenParams<OnboardingStackParamList>;
  Calculator: undefined;
  News: undefined;
  TopAssets: undefined;
  Wallet: NavigatorScreenParams<WalletStackParamList>;
  Lightning: NavigatorScreenParams<LightningStackParamList>;
  Loan: NavigatorScreenParams<LoanStackParamList>;
  Exchange: NavigatorScreenParams<ExchangeStackParamList>;
  More: undefined;
};

export type RootStackParamList = {
  Authenticate: undefined;
  BottomTabs: NavigatorScreenParams<BottomTabParamList> | undefined;
  Settings: undefined;
  Info: undefined;
  Notifications: undefined;
  Loan: undefined;
  Home: undefined;
  PinScreen: {path: string} | undefined;
  VerifySeedPhrase: undefined;
  Keychain: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> = StackScreenProps<
  RootStackParamList,
  T
>;

export type BottomTabScreenProps<T extends keyof BottomTabParamList> =
  CompositeScreenProps<
    RNBottomTabScreenProps<BottomTabParamList, T>,
    RootStackScreenProps<keyof RootStackParamList>
  >;

export type UserLoansTopTabsParamList = {
  Pending: undefined;
  Active: undefined;
  Inactive: undefined;
};
