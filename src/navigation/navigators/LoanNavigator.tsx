import {LoanStackParamList} from '@/navigation/types';
import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import React, {memo, ReactElement} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {PlusIcon} from 'react-native-heroicons/solid';
import {useSelector} from 'react-redux';

import GlobalStyles from '@/constants/GlobalStyles';
import {useLoan} from '@/hooks/redux';
import {RootState} from '@/storage/store';
import {DEFAULT_HEADER_STYLE} from '../utils';
import UserLoansTopTabsNavigator from './UserLoansTopTabsNavigator';

import {KycProvider} from '@/screens/Loan/components/kyc/KycContext';
import RotatingSettingsIcon from '@/screens/Loan/components/ui/RotatingSettingsIcon';
import {
  LoanDetails,
  LoanKYC,
  LoanMpc,
  LoanOnboarding,
  LoanRegister,
  LoanRegistrationSuccess,
  LoanReview,
} from '@/screens/Loan/screens/apply-to-a-loan';
import LoanApproval from '@/screens/Loan/screens/apply-to-a-loan/LoanApproval';
import LoanCollateral from '@/screens/Loan/screens/LoanCollateral';
import LoanLogin from '@/screens/Loan/screens/LoanLogin';
import LoanNewPaymentMethod from '@/screens/Loan/screens/LoanNewPaymentMethod';
import LoanPaymentMethods from '@/screens/Loan/screens/LoanPaymentMethods';
import LoanConfirmSignMPC from '@/screens/Loan/screens/manage-loan/LoanConfirmSignMPC';
import LoanDashboard from '@/screens/Loan/screens/manage-loan/LoanDashboard';
import LoanSettings from '@/screens/Loan/screens/manage-loan/LoanSettings';
import LoanSignMPC from '@/screens/Loan/screens/manage-loan/LoanSignMPC';

const Stack = createStackNavigator<LoanStackParamList>();
const screenOptions: StackNavigationOptions = {
  headerShown: true,
  ...DEFAULT_HEADER_STYLE,
  headerStyle: {
    ...DEFAULT_HEADER_STYLE.headerStyle,
    backgroundColor: GlobalStyles.base.white,
  },
};

const LoanNavigator = (): ReactElement => {
  const {isRegistered} = useLoan();
  const {accessToken} = useSelector((state: RootState) => state.loan);

  const getInitialRoute = () => {
    if (!isRegistered) {
      return 'LoanOnboarding';
    }
    if (accessToken) {
      return 'LoanList';
    }
    if (!accessToken) {
      return 'LoanLogin';
    }
  };

  const initialRouteName = getInitialRoute();

  return (
    <KycProvider>
      <Stack.Navigator screenOptions={screenOptions} initialRouteName={initialRouteName}>
        <Stack.Screen
          name="LoanOnboarding"
          component={LoanOnboarding}
          options={() => ({
            title: 'Welcome',
          })}
        />

        <Stack.Screen
          name="LoanDetails"
          component={LoanDetails}
          options={() => ({
            headerTitle: 'Loan Application',
          })}
        />

        <Stack.Screen name="LoanReview" component={LoanReview} />

        <Stack.Screen
          name="LoanLogin"
          component={LoanLogin}
          options={() => ({
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          })}
        />

        <Stack.Screen
          name="LoanRegister"
          component={LoanRegister}
          options={() => ({
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          })}
        />

        <Stack.Screen
          name="LoanRegistrationSuccess"
          component={LoanRegistrationSuccess}
          options={() => ({
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          })}
        />
        <Stack.Screen
          name="LoanKYC"
          component={LoanKYC}
          options={() => ({
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          })}
        />
        <Stack.Screen
          name="LoanApproval"
          component={LoanApproval}
          options={{headerShown: false}}
        />
        <Stack.Screen name="LoanMpc" component={LoanMpc} options={{headerShown: false}} />
        <Stack.Screen
          name="LoanPaymentMethods"
          component={LoanPaymentMethods}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="LoanNewPaymentMethod"
          component={LoanNewPaymentMethod}
          options={{
            headerShown: true,
            title: 'New Payment Method',
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          }}
        />
        <Stack.Screen
          name="LoanCollateral"
          component={LoanCollateral}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="LoanConfirmSignMPC"
          component={LoanConfirmSignMPC}
          options={() => ({
            headerShown: true,
            headerTitle: 'Complete Your Loan',
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          })}
        />
        <Stack.Screen
          name="LoanSignMPC"
          component={LoanSignMPC}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="LoanList"
          component={UserLoansTopTabsNavigator}
          options={({navigation}) => ({
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
            headerTitle: 'My Loans',
            headerTitleAlign: 'left',
            headerTitleStyle: {
              ...DEFAULT_HEADER_STYLE.headerTitleStyle,
              fontSize: 20,
              color: GlobalStyles.base.black,
            },
            headerRight: () => (
              <View
                style={{
                  marginRight: 6,
                  gap: 24,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <TouchableOpacity
                  onPress={() => navigation.navigate('LoanDetails')}
                  hitSlop={20}
                >
                  <PlusIcon size={30} color={GlobalStyles.gray.gray900} />
                </TouchableOpacity>

                <RotatingSettingsIcon />
              </View>
            ),
          })}
        />

        <Stack.Screen
          name="LoanDashboard"
          component={LoanDashboard}
          options={() => ({
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
            headerTitle: 'Loan Overview',
          })}
        />

        <Stack.Screen
          name="LoanSettings"
          component={LoanSettings}
          options={() => ({
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
            headerTitle: 'Settings',
          })}
        />
      </Stack.Navigator>
    </KycProvider>
  );
};

export default memo(LoanNavigator);
