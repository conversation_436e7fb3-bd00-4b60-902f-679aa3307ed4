#ifndef SCHNORR_MPC_H
#define SCHNORR_MPC_H

namespace  {

constexpr static const uint16_t VERSION = 1;

extern "C" {

void free_string(char *ptr);

/// generateEncryptionKey() -> *char (hex)
char *generateEncryptionKey();

/// getPublicKey(hex_sk) -> *char (hex_pk)
char *getPublicKey(const char *hex_sk);

/// processRound1(json) -> *char (Round1Result as JSON)
char *processRound1(const char *req_json);

/// processRound2(json) -> *char (Round2Result as JSON)
char *processRound2(const char *req_json);

/// processRound3(json) -> *char (Keyshare as JSON)
char *processRound3(const char *req_json);

char *startSign(const char *req_json);

char *signProcessRound1(const char *req_json);

char *signProcessRound2(const char *req_json);

char *signProcessRound3(const char *req_json);

} // extern "C"

} // namespace 

#endif // SCHNORR_MPC_H
