#include "NativeSampleModule.h"
#include "libmpc_core_bindings.h"
#include "schnorr_mpc.h"

namespace facebook::react {


// Helper function to convert C string to std::string
std::string cstr_to_stdstring(const char* cstr) {
  if (cstr == nullptr) {
    return "";
  }
  return std::string(cstr);
}

/* ─── helpers ──────────────────────────────────────────────────── */
static std::string toStd(const char *p) {
  return p ? std::string(p) : std::string{};
}
extern "C" void free_string(char *); // from both C headers
static std::string adopt(const char *p) {
  std::string s = toStd(p);
  free_string(const_cast<char *>(p));
  return s;
}


NativeSampleModule::NativeSampleModule(std::shared_ptr<CallInvoker> jsInvoker)
    : NativeSampleModuleCxxSpec(std::move(jsInvoker)) {}

std::string NativeSampleModule::reverseString(jsi::Runtime& rt, std::string input) {
  return std::string(input.rbegin(), input.rend());
}

// Implementation of dkgPhase1
std::string NativeSampleModule::dkg_phase1(jsi::Runtime& rt, std::string data) {
  const char* input = data.c_str();
  const char* result = dkls_dkg_phase1(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

// Implementation of dkgPhase1
std::string NativeSampleModule::dkg_phase2(jsi::Runtime& rt, std::string phase2_json_in) {
  const char* input = phase2_json_in.c_str();
  const char* result = dkls_dkg_phase2(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::dkg_phase3(jsi::Runtime& rt, std::string phase3_json_in) {
  const char* input = phase3_json_in.c_str();
  const char* result = dkls_dkg_phase3(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::dkg_phase4(jsi::Runtime& rt, std::string phase4_json_in) {
  const char* input = phase4_json_in.c_str();
  const char* result = dkls_dkg_phase4(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}


// Implementation of dkgPhase1
std::string NativeSampleModule::sign_phase1(jsi::Runtime& rt, std::string phase1_json_in) {
  const char* input = phase1_json_in.c_str();
  const char* result = dkls_sign_phase1(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_sign_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

// Implementation of dkgPhase1
std::string NativeSampleModule::sign_phase2(jsi::Runtime& rt, std::string phase2_json_in) {
  const char* input = phase2_json_in.c_str();
  const char* result = dkls_sign_phase2(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_sign_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::sign_phase3(jsi::Runtime& rt, std::string phase3_json_in) {
  const char* input = phase3_json_in.c_str();
  const char* result = dkls_sign_phase3(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::sign_phase4(jsi::Runtime& rt, std::string phase4_json_in) {
  const char* input = phase4_json_in.c_str();
  const char* result = dkls_sign_phase4(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::party_derive_from_path(jsi::Runtime& rt, std::string json_in) {
  const char* input = json_in.c_str();
  const char* result = dkls_party_derive_from_path(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: party_derive_from_path returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

/* ╔════════════════════ Schnorr-MPC helpers ═══════════════════╗ */
std::string NativeSampleModule::schnorr_generate_sk(jsi::Runtime&) {
  return adopt(::generateEncryptionKey());
}
std::string NativeSampleModule::schnorr_get_pk(jsi::Runtime&, std::string sk) {
  return adopt(::getPublicKey(sk.c_str()));
}

/* ╔══════════════ Schnorr-MPC DKG style (3 rounds) ══════════════╗ */
std::string NativeSampleModule::schnorr_process_r1(jsi::Runtime&, std::string j) {
  return adopt(::processRound1(j.c_str()));
}
std::string NativeSampleModule::schnorr_process_r2(jsi::Runtime&, std::string j) {
  return adopt(::processRound2(j.c_str()));
}
std::string NativeSampleModule::schnorr_process_r3(jsi::Runtime&, std::string j) {
  return adopt(::processRound3(j.c_str()));
}

/* ╔════════════ Schnorr-MPC signing (4 rounds) ══════════════════╗ */
std::string NativeSampleModule::schnorr_sign_start(jsi::Runtime&, std::string j) {
  return adopt(::startSign(j.c_str()));
}
std::string NativeSampleModule::schnorr_sign_r1(jsi::Runtime&, std::string j) {
  return adopt(::signProcessRound1(j.c_str()));
}
std::string NativeSampleModule::schnorr_sign_r2(jsi::Runtime&, std::string j) {
  return adopt(::signProcessRound2(j.c_str()));
}
std::string NativeSampleModule::schnorr_sign_r3(jsi::Runtime&, std::string j) {
  return adopt(::signProcessRound3(j.c_str()));
}

} // namespace facebook::react