{"private": true, "name": "assetify-app", "version": "1.0.10", "scripts": {"start": "react-native start", "ios": "yarn react-native run-ios --simulator='iPhone 15 Pro Max'", "ios:se": "yarn react-native run-ios --simulator='iPhone SE (3rd generation)'", "android": "yarn react-native run-android --list-devices", "tsc:check": "tsc -p tsconfig.json --noEmit", "lint:check": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:write": "eslint . --fix --ext .js,.jsx,.ts,.tsx", "clean": "yarn clean:rn && yarn clean:ios && yarn clean:android && yarn start --reset-cache", "clean:rn": "watchman watch-del-all && npx del-cli node_modules && yarn", "clean:ios": "cd ios && npx del-cli Pods Podfile.lock build && pod deintegrate && pod install", "clean:android": "cd android && npx del-cli build app/build app/release", "bundle:ios": "npx react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/AssetifyApp/main.jsbundle --assets-dest ios && open ./ios/AssetifyApp", "postinstall": "yarn patch-package", "postversion": "node version.js"}, "dependencies": {"@AssetifyNet/cryptoapis-kms": "0.7.37", "@bitcoin-js/tiny-secp256k1-asmjs": "^2.2.3", "@breeztech/react-native-breez-sdk": "0.6.4", "@gorhom/bottom-sheet": "4.6.4", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/blur": "^4.4.1", "@react-native-community/cli": "15.0.0", "@react-native-community/cli-platform-android": "15.0.0", "@react-native-community/cli-platform-ios": "15.0.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.3.2", "@react-native-firebase/analytics": "20.5.0", "@react-native-firebase/app": "20.5.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-native/gradle-plugin": "^0.76.2", "@react-navigation/bottom-tabs": "6.6.1", "@react-navigation/material-top-tabs": "6.6.14", "@react-navigation/native": "6.1.18", "@react-navigation/stack": "6.4.1", "@reduxjs/toolkit": "^2.2.5", "@sentry/react-native": "^6.3.0", "@shopify/flash-list": "^1.8.0", "@solana/kit": "^2.1.0", "@solana/web3.js": "^1.98.1", "@tanstack/react-query": "^5.52.1", "@tradle/react-native-http": "^2.0.0", "@types/big.js": "^6.2.2", "assert": "^2.1.0", "axios": "^1.7.2", "big.js": "^6.2.2", "bip32": "^5.0.0-rc.0", "bip39": "^3.1.0", "bitcoinjs-lib": "^6.1.7", "bitcore-lib-cash": "^11.0.0", "blake2b-wasm": "^2.4.0", "bls-signatures": "^2.0.3", "bolt11": "^1.4.1", "buffer": "^6.0.3", "ecpair": "^3.0.0", "ethers": "^6.13.0", "events": "^3.3.0", "hdkey": "^2.1.0", "https-browserify": "^0.0.1", "i18next": "^23.11.5", "lodash": "^4.17.21", "lottie-react-native": "^6.7.2", "patch-package": "^8.0.0", "process": "^0.11.10", "protobufjs": "^7.4.0", "querystring-es3": "^0.2.1", "react": "18.3.1", "react-i18next": "^14.1.2", "react-native": "0.76.0", "react-native-biometrics": "^3.0.1", "react-native-camera-kit": "^14.0.0", "react-native-codegen": "^0.70.7", "react-native-country-flag": "^2.0.2", "react-native-device-info": "^13.1.0", "react-native-dotenv": "^3.4.11", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.20.2", "react-native-haptic-feedback": "^2.3.3", "react-native-heroicons": "^4.0.0", "react-native-http": "github:tradle/react-native-http#834492d", "react-native-image-picker": "^8.2.1", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-keyboard-accessory": "^0.1.16", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keychain": "^9.2.3", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^3.2.0", "react-native-onesignal": "5.2.0", "react-native-os": "^1.2.6", "react-native-pager-view": "6.5.1", "react-native-path": "^0.0.5", "react-native-permissions": "3.6.0", "react-native-quick-crypto": "^0.7.12", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^4.10.1", "react-native-screens": "4.0.0", "react-native-skeleton-placeholder": "5.2.4", "react-native-stream": "^0.1.9", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.5.0", "react-native-tab-view": "3.5.2", "react-native-tcp-socket": "^6.2.0", "react-native-toast-message": "^2.3.3", "react-native-udp": "4.1.7", "react-native-url-polyfill": "^2.0.0", "react-native-wagmi-charts": "^2.7.1", "react-native-webview": "13.13.4", "react-number-format": "^5.4.2", "react-redux": "^9.1.2", "readable-stream": "1.0.33", "redux": "^5.0.1", "redux-persist": "^6.0.0", "rn-android-keyboard-adjust": "^2.1.2", "secp256k1": "^5.0.0", "socket.io-client": "^4.8.1", "stream-browserify": "^1.0.0", "string_decoder": "^0.10.31", "styled-components": "^6.1.13", "text-encoding-polyfill": "^0.6.7", "tronweb": "^5.3.2", "xrpl": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native/babel-preset": "0.76.1", "@react-native/eslint-config": "0.76.1", "@react-native/metro-config": "0.76.1", "@react-native/typescript-config": "0.76.1", "@types/lodash": "^4.17.4", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@types/styled-components-react-native": "^5.2.5", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "rn-nodeify": "github:tradle/rn-nodeify", "typescript": "5.0.4"}, "react-native": {"crypto": "react-native-quick-crypto", "net": "react-native-tcp-socket", "http": "@tradle/react-native-http", "https": "https-browserify", "os": "react-native-os", "fs": "react-native-fs", "_stream_transform": "readable-stream/transform", "_stream_readable": "readable-stream/readable", "_stream_writable": "readable-stream/writable", "_stream_duplex": "readable-stream/duplex", "_stream_passthrough": "readable-stream/passthrough", "dgram": "react-native-udp", "stream": "stream-browserify", "events": "events", "buffer": "buffer"}, "engines": {"node": ">=18"}, "codegenConfig": {"name": "AppSpecs", "type": "modules", "jsSrcsDir": "specs", "android": {"javaPackageName": "com.assetifyapp.specs"}}, "packageManager": "yarn@1.22.21"}