/* ─── <PERSON><PERSON><PERSON> ─────────────────────────────────────────────── */

export interface PartyEncryptionKey {
    party_id: number;
    public_key: string;       // hex-encoded
  }
  
  /** R0 → R1 */
  export interface SchnorrDkgRound0Input {
    t: number;
    n: number;
    party_id: number;          // same name as Rust
    decryption_key: string;    // hex
    all_parties_keys: PartyEncryptionKey[];
  }
  
  export interface SchnorrDkgRound0Output {
    party_state_id: string;    // u64 encoded as decimal string
    broadcast_message: string; // hex(KeygenMsg1)
  }
  
  /** R1 → R2 */
  export interface SchnorrDkgRound1Input {
    party_state_id: number;
    messages: string[];        // array of hex(KeygenMsg1)
  }
  
  export interface SchnorrDkgRound1Output {
    party_state_id: string;
    broadcast_message: string; // hex(KeygenMsg2)
  }
  
  /** R2 → keyshare */
  export interface Sc<PERSON><PERSON>rDkgRound2Input {
    party_state_id: number;
    messages: string[];        // array of hex(KeygenMsg2)
  }
  
  export interface SchnorrDkgRound2Output {
    keyshare: {
      threshold: number;
      total_parties: number;
      party_id: number;
      d_i: string;           // hex scalar
      public_key: string;    // hex compressed point
      key_id: string;        // 32-byte hex
      root_chain_code: string; // 32-byte hex
      extra_data: string | null;
    };
  }


  /* ─── Schnorr MPC – signature protocol ────────────────────────── */

/** Round-0 (startSign) — initialise a signing session */
export interface SchnorrSignRound0Input {
    /** The local key-share as JSON (exact string returned by capi.processRound3). */
    keyshare_json: string;
    /** Message to sign – ASCII/UTF-8 text, supplied as full-length hex. */
    message_hex: string;
    /** BIP-32 style derivation path, e.g. `"m/0"` or `"m/44'/0'/0'/0/3"`. */
    deriv_path: string;
  }
  
  export interface SchnorrSignRound0Output {
    /** Opaque handle for the signer state (decimal-encoded `u64`). */
    party_state_id: number;
    /** Hex-encoded `SignMsg1` that must be broadcast to all peers. */
    broadcast_message: string;
  }
  
  /** Round-1 (signProcessRound1) — after collecting *all* SignMsg1 messages */
  export interface SchnorrSignRound1Input {
    party_state_id: number;
    /** Every peer’s hex-encoded `SignMsg1` (order doesn’t matter). */
    messages: string[];
  }
  
  export interface SchnorrSignRound1Output {
    party_state_id: number;
    /** Hex-encoded `SignMsg2` to broadcast. */
    broadcast_message: string;
  }
  
  /** Round-2 (signProcessRound2) — after collecting *all* SignMsg2 messages */
  export interface SchnorrSignRound2Input {
    party_state_id: number;
    messages: string[];            // hex SignMsg2
  }
  
  export interface SchnorrSignRound2Output {
    party_state_id: number;
    /** Hex-encoded `SignMsg3` to broadcast. */
    broadcast_message: string;
  }
  
  /** Round-3 (signProcessRound3) — after collecting *all* SignMsg3 messages */
  export interface SchnorrSignRound3Input {
    party_state_id: number;
    messages: string[];            // hex SignMsg3
  }
  
  export interface SchnorrSignRound3Output {
    /** Final Schnorr signature (64-byte hex, R‖s). */
    signature: string;
  }
  

  export interface SchnorrParty {
    party_id: number,
    threshold: number,
    total_parties: number,

    public_key: Uint8Array,
    root_chain_code: Uint8Array,
    d_i: Uint8Array,
    extra_data: any,
    key_id: Uint8Array,

    session_id_ws: string,
    solana_address: string,
  }
  