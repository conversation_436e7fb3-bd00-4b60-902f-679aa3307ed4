import { TurboModule, TurboModuleRegistry } from 'react-native';
import { BroadcastDerivationPhase2to4, BroadcastDerivationPhase3to4, Party, TransmitInitMulPhase3to4, TransmitInitZeroSharePhase2to4, TransmitInitZeroSharePhase3to4 } from './keygen-payloads';
import { Broadcast3to4, SignData, TransmitPhase1to2, TransmitPhase2to3 } from './signature-payloads';
import { AffinePoint, DKGSession, HashOutput, OTReceiver, ProofCommitment, Scalar } from './common-payloads';

export interface DKGPhase1Output {
  fragments: string[]; // Assuming Scalar is serialized as hex string
}

export interface DKGPhase2Input extends DKGSession {
  poly_fragments: string[]; // Assuming Scalar is serialized as hex string
}

export interface DKGPhase2Output {
  poly_point: string; // Assuming Scalar is serialized as hex string
  proof_commitment: ProofCommitment;
  zero_keep: { [key: number]: KeepInitZeroSharePhase2to3 };
  zero_transmit: TransmitInitZeroSharePhase2to4[];
  bip_keep: UniqueKeepDerivationPhase2to3;
  bip_broadcast: BroadcastDerivationPhase2to4;
}

export interface DKGPhase3Input extends DKGSession {
  zero_kept: { [key: number]: KeepInitZeroSharePhase2to3 };
  bip_kept: UniqueKeepDerivationPhase2to3;
}

export interface DKGPhase3Output {
  zero_keep: { [key: number]: KeepInitZeroSharePhase3to4 };
  zero_transmit: TransmitInitZeroSharePhase3to4[];
  mul_keep: { [key: number]: KeepInitMulPhase3to4 };
  mul_transmit: TransmitInitMulPhase3to4[];
  bip_broadcast: BroadcastDerivationPhase3to4;
}

export interface DKGPhase4Input extends DKGSession {
  poly_point: string;
  proofs_commitments: ProofCommitment[];
  zero_kept: { [key: number]: KeepInitZeroSharePhase3to4 };
  zero_received_phase2: TransmitInitZeroSharePhase2to4[];
  zero_received_phase3: TransmitInitZeroSharePhase3to4[];
  mul_kept: { [key: number]: KeepInitMulPhase3to4 };
  mul_received: TransmitInitMulPhase3to4[];
  bip_broadcast_2to4: { [key: number]: BroadcastDerivationPhase2to4 };
  bip_broadcast_3to4: { [key: number]: BroadcastDerivationPhase3to4 };
}


export interface DKGPhase4Output {
  party: Party;
}

/** KEEP PAYLOADS */

export interface KeepInitZeroSharePhase2to3 {
  seed: number[];
  salt: number[];
}

export interface KeepInitZeroSharePhase3to4 {
  seed: number[];
}

export interface KeepInitMulPhase3to4 {
  ot_sender: string;
  nonce: string;
  ot_receiver: string;
  correlation: boolean[];
  vec_r: string[];
}

export interface UniqueKeepDerivationPhase2to3 {
  aux_chain_code: number[];
  cc_salt: number[];
}


// DSG


export interface SignPhase1Input {
  party: Party;
  sign_data: SignData;
}

export interface SignPhase1Output {
  unique_keep: UniqueKeep1to2;
  keep: { [key: number]: KeepPhase1to2 };
  transmit: TransmitPhase1to2[];
}

export interface SignPhase2Input {
  party: Party;
  sign_data: SignData;
  unique_kept: UniqueKeep1to2;
  kept: { [key: number]: KeepPhase1to2 };
  received: TransmitPhase1to2[];
}

export interface SignPhase2Output {
  unique_keep: UniqueKeep2to3;
  keep: { [key: number]: KeepPhase2to3 };
  transmit: TransmitPhase2to3[];
}

export interface SignPhase3Input {
  party: Party;
  sign_data: SignData;
  unique_kept: UniqueKeep2to3;
  kept: { [key: number]: KeepPhase2to3 };
  received: TransmitPhase2to3[];
}

export interface SignPhase3Output {
  x_coord: string;
  broadcast: Broadcast3to4;
}

export interface SignPhase4Input {
  party: Party;
  sign_data: SignData;
  x_coord: string;
  received: Broadcast3to4[];
  normalize: boolean;
}

export interface SignPhase4Output {
  signature: string;
  rec_id: number;
}


/** KEEP PAYLOADS */


export interface UniqueKeep1to2 {
  instance_key: Scalar;
  instance_point: AffinePoint;
  inversion_mask: Scalar;
  zeta: Scalar;
}

export interface KeepPhase1to2 {
  salt: number[]; // Vec<u8>
  chi: Scalar;
  mul_keep: MulDataToKeepReceiver;
}

export interface MulDataToKeepReceiver {
  ot_receiver: OTReceiver;
  nonce: Scalar;
}

export interface UniqueKeep2to3 {
  instance_key: Scalar;
  instance_point: AffinePoint;
  inversion_mask: Scalar;
  key_share: Scalar;
  public_share: AffinePoint;
}

export interface KeepPhase2to3 {
  c_u: Scalar;
  c_v: Scalar;
  commitment: HashOutput;
  mul_keep: MulDataToKeepReceiver;
  chi: Scalar;
}


interface Spec extends TurboModule {
  readonly dkg_phase1: (data: string) => string;
  readonly dkg_phase2: (phase2_json_in: string) => string;
  readonly dkg_phase3: (phase3_json_in: string) => string;
  readonly dkg_phase4: (phase4_json_in: string) => string;

  // DSG

  readonly sign_phase1: (phase1_json_in: string) => string;
  readonly sign_phase2: (phase2_json_in: string) => string;
  readonly sign_phase3: (phase3_json_in: string) => string;
  readonly sign_phase4: (phase3_json_in: string) => string;

  readonly party_derive_from_path: (derive: string) => string;

  readonly schnorr_generate_sk: () => string;
  readonly schnorr_get_pk: (sk: string) => string;
  readonly schnorr_process_r1: (json: string) => string;
  readonly schnorr_process_r2: (json: string) => string;
  readonly schnorr_process_r3: (json: string) => string;
  readonly schnorr_sign_start: (json: string) => string;
  readonly schnorr_sign_r1: (json: string) => string;
  readonly schnorr_sign_r2: (json: string) => string;
  readonly schnorr_sign_r3: (json: string) => string;
}

const NativeSampleModule = TurboModuleRegistry.getEnforcing<Spec>('NativeSampleModule');

class DKGHandler {
  static dkgPhase1(data: DKGSession): DKGPhase1Output {
    const result = NativeSampleModule.dkg_phase1(JSON.stringify(data));
    return JSON.parse(result) as DKGPhase1Output;
  }

  static dkgPhase2(data: DKGPhase2Input): DKGPhase2Output {
    const result = NativeSampleModule.dkg_phase2(JSON.stringify(data));
    return JSON.parse(result) as DKGPhase2Output;
  }

  static dkgPhase3(data: DKGPhase3Input): DKGPhase3Output {
    const result = NativeSampleModule.dkg_phase3(JSON.stringify(data));
    return JSON.parse(result) as DKGPhase3Output;
  }

  static dkgPhase4(data: DKGPhase4Input): DKGPhase4Output {
    const result = NativeSampleModule.dkg_phase4(JSON.stringify(data));
    return JSON.parse(result) as DKGPhase4Output;
  }
    // Signing methods
    static signPhase1(data: SignPhase1Input): SignPhase1Output {
      const result = NativeSampleModule.sign_phase1(JSON.stringify(data));
      return JSON.parse(result) as SignPhase1Output;
    }
  
    static signPhase2(data: SignPhase2Input): SignPhase2Output {
      const result = NativeSampleModule.sign_phase2(JSON.stringify(data));
      return JSON.parse(result) as SignPhase2Output;
    }
  
    static signPhase3(data: SignPhase3Input): SignPhase3Output {
      const result = NativeSampleModule.sign_phase3(JSON.stringify(data));
      return JSON.parse(result) as SignPhase3Output;
    }

    static signPhase4(data: SignPhase4Input): SignPhase4Output {
      const result = NativeSampleModule.sign_phase4(JSON.stringify(data));
      return JSON.parse(result) as SignPhase4Output;
    } 

    static partyDeriveFromPath(data: any): DKGPhase4Output {
      const result = NativeSampleModule.party_derive_from_path(JSON.stringify(data));
      return JSON.parse(result) as DKGPhase4Output;
    } 

    static schnorrGenerateSk(): string {
      return NativeSampleModule.schnorr_generate_sk();
    }

    static schnorrGetPk(sk: string): string {
      return NativeSampleModule.schnorr_get_pk(sk);
    }

    static schnorrProcessR1(json: string): string {
      return NativeSampleModule.schnorr_process_r1(json);
    }

    static schnorrProcessR2(json: string): string {
      return NativeSampleModule.schnorr_process_r2(json);
    }

    static schnorrProcessR3(json: string): string {
      return NativeSampleModule.schnorr_process_r3(json);
    }

    static schnorrSignStart(json: string): string {
      return NativeSampleModule.schnorr_sign_start(json);
    }

    static schnorrSignR1(json: string): string {
      return NativeSampleModule.schnorr_sign_r1(json);
    }

    static schnorrSignR2(json: string): string {
      return NativeSampleModule.schnorr_sign_r2(json);
    }

    static schnorrSignR3(json: string): string {
      return NativeSampleModule.schnorr_sign_r3(json);
    }
}

export default DKGHandler;
